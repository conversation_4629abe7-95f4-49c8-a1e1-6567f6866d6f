package com.labway.lims.api;

import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import java.util.Collections;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

public class RedisDistributedLock {

    private final StringRedisTemplate redisTemplate;
    private final String lockKey;
    private final String lockValue;
    private final long expireTime; // 毫秒
    private volatile boolean isLocked = false;
    private Thread watchdogThread;

    // Lua脚本：释放锁
    private static final String UNLOCK_SCRIPT =
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                    "    return redis.call('del', KEYS[1]) " +
                    "else " +
                    "    return 0 " +
                    "end";

    // Lua脚本：续期锁
    private static final String RENEW_SCRIPT =
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                    "    return redis.call('pexpire', KEYS[1], ARGV[2]) " +
                    "else " +
                    "    return 0 " +
                    "end";

    public RedisDistributedLock(StringRedisTemplate redisTemplate, String lockKey, long expireTime) {
        this.redisTemplate = redisTemplate;
        this.lockKey = lockKey;
        this.expireTime = expireTime;
        this.lockValue = Thread.currentThread().getId() + "-" + UUID.randomUUID();
    }

    /**
     * 尝试获取锁
     */
    public boolean tryLock() {
        Boolean success = redisTemplate.opsForValue().setIfAbsent(
                lockKey,
                lockValue,
                expireTime,
                TimeUnit.MILLISECONDS
        );

        if (Boolean.TRUE.equals(success)) {
            isLocked = true;
            startWatchdog();
            return true;
        }
        return false;
    }

    /**
     * 释放锁
     */
    public boolean unlock() {
        if (!isLocked) {
            return false;
        }

        DefaultRedisScript<Long> script = new DefaultRedisScript<>(UNLOCK_SCRIPT, Long.class);
        Long result = redisTemplate.execute(
                script,
                Collections.singletonList(lockKey),
                lockValue
        );

        if (result == 1) {
            stopWatchdog();
            isLocked = false;
            return true;
        }
        return false;
    }


    /**
     * 启动看门狗线程
     */
    private void startWatchdog() {
        if (watchdogThread != null) {
            return;
        }

        // 看门狗检查间隔设置为过期时间的1/3
        long checkInterval = expireTime / 3;

        watchdogThread = new Thread(() -> {
            while (isLocked) {
                try {
                    Thread.sleep(checkInterval);

                    // 执行续期操作
                    DefaultRedisScript<Long> script = new DefaultRedisScript<>(RENEW_SCRIPT, Long.class);
                    Long result = redisTemplate.execute(
                            script,
                            Collections.singletonList(lockKey),
                            lockValue,
                            String.valueOf(expireTime)
                    );

                    if (result == 0) {
                        // 续期失败，可能是锁已被释放或已过期
                        isLocked = false;
                        break;
                    }

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    // Redis操作异常处理
                    break;
                }
            }
        }, "RedisLockWatchdog-" + lockKey);

        watchdogThread.setDaemon(true);
        watchdogThread.start();
    }

    /**
     * 停止看门狗线程
     */
    private void stopWatchdog() {
        if (watchdogThread != null) {
            watchdogThread.interrupt();
            watchdogThread = null;
        }
    }
}
