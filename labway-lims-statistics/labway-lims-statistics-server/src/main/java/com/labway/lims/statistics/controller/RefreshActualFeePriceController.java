package com.labway.lims.statistics.controller;

import com.labway.lims.api.web.BaseController;
import com.labway.lims.statistics.api.client.RefreshActualFeePriceService;
import com.labway.lims.statistics.dto.RefreshActualFeePriceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashSet;

/**
 * 刷新价格
 */
@Slf4j
@RestController
@RequestMapping("/preprocess-statistics")
public class RefreshActualFeePriceController extends BaseController {

    @Resource
    private RefreshActualFeePriceService refreshActualFeePriceService;

    @PostMapping("/refresh-actual-fee-price")
    public Object refreshActualFeePrice(@RequestBody RefreshActualFeePriceDto dto) {
        return refreshActualFeePriceService.refreshActualFeePrice(dto.getStartDate(), dto.getEndDate(), new HashSet<>(dto.getHspOrgIds()));
    }

}
