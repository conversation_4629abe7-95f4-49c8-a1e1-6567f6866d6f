package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.Dict;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.HuaweiObsUtils;
import com.labway.lims.api.PatientAges;
import com.labway.lims.api.enums.PdfTemplateTypeEnum;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.ReportTemplateBindTypeEnum;
import com.labway.lims.api.enums.specialty.SpecialtySampleResultTemplateTypeEnum;
import com.labway.lims.api.exception.LimsException;
import com.labway.lims.apply.api.dto.ReportMergePrintInfoDto;
import com.labway.lims.apply.api.dto.SampleEsQuery;
import com.labway.lims.apply.api.dto.es.BaseSampleEsModelDto;
import com.labway.lims.apply.api.dto.es.GeneticsInspectionDto;
import com.labway.lims.apply.api.dto.es.InfectionInspectionDto;
import com.labway.lims.apply.api.dto.es.MicrobiologyInspectionDto;
import com.labway.lims.apply.api.dto.es.OutsourcingInspectionDto;
import com.labway.lims.apply.api.dto.es.RoutineInspectionDto;
import com.labway.lims.apply.api.dto.es.SpecialtyInspectionDto;
import com.labway.lims.apply.api.service.SampleImageService;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ProfessionalGroupDto;
import com.labway.lims.base.api.dto.ReportTemplateBindDto;
import com.labway.lims.base.api.dto.UserDto;
import com.labway.lims.base.api.service.GroupService;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import com.labway.lims.base.api.service.ReportTemplateBindService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.pdfreport.api.dto.PdfReportParamDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.statistics.api.client.ElasticSearchSampleService;
import com.labway.lims.statistics.api.client.ReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.apache.pdfbox.pdmodel.PDDocumentInformation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <pre>
 * ReportServiceImpl
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/8/9 14:09
 */
@Slf4j
@DubboService
@RefreshScope
public class ReportServiceImpl implements ReportService {

    // 合并文件临时目录
    private static final String MERGE_TEMP_DIR = System.getProperty("user.dir") + "/merge_temp";
    private static final String DATE_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DATE_PATTERN_SIMPLE = "yyyy-MM-dd HH:mm";

    @DubboReference
    private UserService userService;
    @DubboReference
    private GroupService groupService;

    @DubboReference
    private SampleImageService sampleImageService;
    @Resource
    private ElasticSearchSampleService elasticSearchSampleService;
    @DubboReference
    private PdfReportService pdfReportService;
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;
    @DubboReference
    private ReportTemplateBindService reportTemplateBindService;
    @Resource
    private ReportService reportService;
    @Resource
    private HuaweiObsUtils huaweiObsUtils;

    // 强制合并报告单的送检机构
    @Value("${split-blood.forced-merge.hsp-org-code:123}")
    private List<String> forcedMergeHspOrgCodeList = new ArrayList<>();

    /**
     * 合并报告单 填充一些额外信息，如：签名图片，样本图片等
     */
    @Override
    public ReportMergePrintInfoDto populateExtraInfo(
            ReportMergePrintInfoDto printInfoDto, BaseSampleEsModelDto sampleEsModelDto) {

        final ProfessionalGroupDto group = groupService.selectByGroupId(sampleEsModelDto.getGroupId());
        if (Objects.isNull(group)) {
            throw new IllegalStateException(String.format("专业组 [%s] 不存在", sampleEsModelDto.getGroupName()));
        }
        final UserDto finalChecker = userService.selectByUserId(sampleEsModelDto.getFinalCheckerId());
        if (Objects.isNull(finalChecker)) {
            throw new IllegalStateException(String.format("终审人人 [%s] 不存在", sampleEsModelDto.getFinalCheckerName()));
        }

        final UserDto tester = userService.selectByUserId(sampleEsModelDto.getTesterId());
        if (Objects.isNull(tester)) {
            throw new IllegalStateException(String.format("检验者 [%s] 不存在", sampleEsModelDto.getTesterName()));
        }

        // 检验者，审核者，批准者签名
        printInfoDto.setSignature(Dict.of(
                // 检验者
                "tester",
                Dict.of("name", tester.getNickname(),
                        "cnSign", tester.getCnSign(),
                        "enSign", tester.getEnSign(),
                        "sign", StringUtils.defaultString(tester.getCnSign(), tester.getEnSign())),
                // 最终审核人
                "finalChecker",
                Dict.of("name", finalChecker.getNickname(),
                        "cnSign", finalChecker.getCnSign(),
                        "enSign", finalChecker.getEnSign(),
                        "sign", StringUtils.defaultString(finalChecker.getCnSign(), finalChecker.getEnSign())),
                // 批准者
                "approver",
                Dict.of("name", group.getApproverName(),
                        "sign", group.getApproverSign())));
        // 样本图片
        printInfoDto.setSampleImages(sampleImageService.selectSampleImageBySampleId(sampleEsModelDto.getSampleId()).stream().map(Dict::parse)
                .collect(Collectors.toList()));

        return printInfoDto;
    }

    @Override
    public List<String> mergePrint(Set<Long> applySampleIds) {
        if (CollectionUtils.isEmpty(applySampleIds)) {
            throw new LimsException("存在必填项未填写");
        }
        final List<BaseSampleEsModelDto> esIdBySampleEsQueryDtoList =
                elasticSearchSampleService.selectSamples(SampleEsQuery.builder().applySampleIds(applySampleIds).build());
        if (CollectionUtils.isEmpty(esIdBySampleEsQueryDtoList)) {
            return Collections.emptyList();
        }

        final Set<String> hspOrgCodeSet = esIdBySampleEsQueryDtoList.stream().map(BaseSampleEsModelDto::getHspOrgCode).collect(Collectors.toSet());
        if (hspOrgCodeSet.size() > 1) {
            throw new LimsException(String.format("报告单合并打印，所选样本存在多个送检机构：[%s]", StringUtils.join(hspOrgCodeSet, ",")));
        }
        // 这里只有一个
        final String hspOrgCode = hspOrgCodeSet.iterator().next();

        // 特检样本
        Map<Long, Long> specialtyTestItemIdById = new HashMap<>();
        esIdBySampleEsQueryDtoList.stream().filter(SpecialtyInspectionDto.class::isInstance).forEach(item -> {
            SpecialtyInspectionDto modelDto = (SpecialtyInspectionDto) item;
            if (CollectionUtils.isNotEmpty(modelDto.getTestItems())) {
                // 特检 一个样本只有一个检验项目
                specialtyTestItemIdById.put(modelDto.getSampleId(),
                        modelDto.getTestItems().get(NumberUtils.INTEGER_ZERO).getTestItemId());
            }
        });
        // 特检
        Map<Long, SpecialtySampleResultTemplateTypeEnum> resultTemplateByTestItemId = new HashMap<>();
        if (!specialtyTestItemIdById.isEmpty()) {
            // 使用模版类型
            List<ReportTemplateBindDto> reportTemplateBindDtos = reportTemplateBindService
                    .selectByBizIdsAndBindType(specialtyTestItemIdById.values(), ReportTemplateBindTypeEnum.TEST_ITEM);
            resultTemplateByTestItemId =
                    reportTemplateBindDtos.stream().collect(Collectors.toMap(ReportTemplateBindDto::getBizId,
                            obj -> SpecialtySampleResultTemplateTypeEnum.getStatusByTemplateCode(obj.getReportTemplateCode())));
        }

        // 条码号 分组
        Map<String, List<BaseSampleEsModelDto>> groupingByBarcode =
                esIdBySampleEsQueryDtoList.stream().collect(Collectors.groupingBy(BaseSampleEsModelDto::getBarcode));

        List<ReportMergePrintInfoDto> targetList = Lists.newArrayList();
        // 所有涉及 报告项目id、仪器id
        Set<String> reportItemCodes = new HashSet<>();
        Set<Long> instrumentIds = new HashSet<>();
        for (Map.Entry<String, List<BaseSampleEsModelDto>> entry : groupingByBarcode.entrySet()) {
            List<BaseSampleEsModelDto> value = entry.getValue();
            BaseSampleEsModelDto sampleEsModelDto = value.get(NumberUtils.INTEGER_ZERO);
            ReportMergePrintInfoDto temp = new ReportMergePrintInfoDto();
            temp.setPatientName(sampleEsModelDto.getPatientName());
            temp.setPatientVisitCard(sampleEsModelDto.getPatientVisitCard());
            temp.setReportNo("");
            temp.setHspOrgCode(sampleEsModelDto.getHspOrgCode());
            temp.setHspOrgName(sampleEsModelDto.getHspOrgName());
            temp.setPatientSex(SexEnum.getByCode(sampleEsModelDto.getPatientSex()).getDesc());
            temp.setDept(sampleEsModelDto.getDept());
            temp.setSampleTypeName(sampleEsModelDto.getSampleTypeName());
            temp.setSampleNo(sampleEsModelDto.getSampleNo());
            temp.setPatientAge(PatientAges.toText(sampleEsModelDto));
            temp.setPatientBed(sampleEsModelDto.getPatientBed());
            temp.setDiagnosis(sampleEsModelDto.getDiagnosis());
            temp.setOrgName(sampleEsModelDto.getOrgName());
            temp.setSendDoctorCode(sampleEsModelDto.getSendDoctorCode());
            temp.setSendDoctorName(sampleEsModelDto.getSendDoctorName());
            temp.setGroupName(sampleEsModelDto.getGroupName());
            temp.setBarcode(sampleEsModelDto.getBarcode());
            temp.setRemark(sampleEsModelDto.getRemark());
            temp.setSampleRemark(sampleEsModelDto.getSampleRemark());
            temp.setResultRemark(sampleEsModelDto.getResultRemark());
            temp.setRemark(sampleEsModelDto.getSampleRemark());
            temp.setSamplingDate(DateUtil.format(sampleEsModelDto.getSamplingDate(), DATE_PATTERN_SIMPLE));
            temp.setCreateDate(DateUtil.format(sampleEsModelDto.getCreateDate(), DATE_PATTERN_SIMPLE));
            temp.setFinalCheckDate(DateUtil.format(sampleEsModelDto.getFinalCheckDate(), DATE_PATTERN_SIMPLE));
            temp.setApplyTypeName(sampleEsModelDto.getApplyTypeName());
            temp.setSampleStatus(sampleEsModelDto.getSampleStatus());
            temp.setTesterId(sampleEsModelDto.getTesterId());
            temp.setTesterName(sampleEsModelDto.getTesterName());
            temp.setFinalCheckerId(sampleEsModelDto.getFinalCheckerId());
            temp.setFinalCheckerName(sampleEsModelDto.getFinalCheckerName());
            temp.setSampleProperty(sampleEsModelDto.getSampleProperty());
            temp.setReportUrls(sampleEsModelDto.getReports().stream().map(BaseSampleEsModelDto.Report::getUrl).collect(Collectors.toList()));

            // 设置样本图片，签名图片等字段
            reportService.populateExtraInfo(temp, sampleEsModelDto);

            List<ReportMergePrintInfoDto.GroupResult> groupResultList = Lists.newArrayList();

            // 样本 对应结果
            for (BaseSampleEsModelDto esModelDto : value) {
                ReportMergePrintInfoDto.GroupResult result = new ReportMergePrintInfoDto.GroupResult();
                result.setGroupName(esModelDto.getGroupName());
                result.setSampleNo(esModelDto.getSampleNo());
                result.setSampleTypeName(esModelDto.getSampleTypeName());
                result.setRemark(esModelDto.getRemark());
                result.setSampleRemark(esModelDto.getSampleRemark());
                result.setResultRemark(esModelDto.getResultRemark());
                result.setInstrumentId(esModelDto.getInstrumentId());
                String resultType = StringUtils.EMPTY;
                BaseSampleEsModelDto resultValue = null;
                List<ReportMergePrintInfoDto.ReportItemResult> reportItemResulList = Lists.newArrayList();
                if (esModelDto instanceof RoutineInspectionDto) {
                    // 常规
                    RoutineInspectionDto modelDto = (RoutineInspectionDto) esModelDto;
                    instrumentIds.add(esModelDto.getInstrumentId());
                    // 对应 检验结果
                    List<RoutineInspectionDto.RoutineReportItem> reportItems =
                            ObjectUtils.defaultIfNull(modelDto.getReportItems(), Collections.emptyList());
                    reportItems.forEach(reportItem -> {
                        ReportMergePrintInfoDto.ReportItemResult reportItemResult =
                                new ReportMergePrintInfoDto.ReportItemResult();
                        reportItemResult.setReportItemCode(reportItem.getReportItemCode());
                        reportItemResult.setReportItemName(reportItem.getReportItemName());
                        reportItemResult.setResult(reportItem.getResult());
                        reportItemResult.setRange(reportItem.getRange());
                        reportItemResult.setJudge(reportItem.getJudge());
                        reportItemResult.setUnit(reportItem.getUnit());
                        reportItemResult.setInstrumentId(reportItem.getInstrumentId());
                        reportItemCodes.add(reportItemResult.getReportItemCode());
                        instrumentIds.add(reportItemResult.getInstrumentId());
                        reportItemResulList.add(reportItemResult);
                    });
                } else if (esModelDto instanceof GeneticsInspectionDto) {
                    // 遗传
                    GeneticsInspectionDto modelDto = (GeneticsInspectionDto) esModelDto;
                    resultType = ItemTypeEnum.GENETICS.name();
                    resultValue = modelDto;
                } else if (esModelDto instanceof InfectionInspectionDto) {
                    // 院感
                    InfectionInspectionDto modelDto = (InfectionInspectionDto) esModelDto;
                    instrumentIds.add(esModelDto.getInstrumentId());
                    // 对应 检验结果
                    List<InfectionInspectionDto.InfectionReportItem> reportItems =
                            ObjectUtils.defaultIfNull(modelDto.getReportItems(), Collections.emptyList());
                    reportItems.forEach(reportItem -> {
                        ReportMergePrintInfoDto.ReportItemResult reportItemResult =
                                new ReportMergePrintInfoDto.ReportItemResult();
                        reportItemResult.setReportItemCode(reportItem.getReportItemCode());
                        reportItemResult.setReportItemName(reportItem.getReportItemName());
                        reportItemResult.setResult(reportItem.getResult());
                        reportItemResult.setRange(reportItem.getRange());
                        reportItemResult.setJudge(reportItem.getJudge());
                        reportItemResult.setUnit(reportItem.getUnit());
                        reportItemResult.setInstrumentId(reportItem.getInstrumentId());
                        reportItemCodes.add(reportItemResult.getReportItemCode());
                        instrumentIds.add(reportItemResult.getInstrumentId());
                        reportItemResulList.add(reportItemResult);
                    });
                } else if (esModelDto instanceof MicrobiologyInspectionDto) {
                    // 微生物
                    MicrobiologyInspectionDto modelDto = (MicrobiologyInspectionDto) esModelDto;
                    resultType = ItemTypeEnum.MICROBIOLOGY.name();
                    resultValue = modelDto;
                } else if (esModelDto instanceof SpecialtyInspectionDto) {
                    // 特检
                    SpecialtyInspectionDto modelDto = (SpecialtyInspectionDto) esModelDto;
                    resultType = ItemTypeEnum.SPECIALTY.name();
                    resultValue = modelDto;
                    if (CollectionUtils.isNotEmpty(modelDto.getTestItems())) {
                        // 特检 一个样本只有一个检验项目
                        Long testItemId = specialtyTestItemIdById.put(modelDto.getSampleId(),
                                modelDto.getTestItems().get(NumberUtils.INTEGER_ZERO).getTestItemId());
                        SpecialtySampleResultTemplateTypeEnum typeEnum =
                                ObjectUtils.defaultIfNull(resultTemplateByTestItemId.get(testItemId),
                                        SpecialtySampleResultTemplateTypeEnum.SPECIALTY_STANDARD);
                        result.setReportTemplateCode(typeEnum.getReportTemplateCode());
                    }
                } else if (esModelDto instanceof OutsourcingInspectionDto) {
                    // 外送检验
                    OutsourcingInspectionDto modelDto = (OutsourcingInspectionDto) esModelDto;
                    instrumentIds.add(esModelDto.getInstrumentId());
                    // 对应 检验结果
                    List<OutsourcingInspectionDto.OutsourcingReportItem> reportItems =
                            ObjectUtils.defaultIfNull(modelDto.getReportItems(), Collections.emptyList());
                    reportItems.forEach(reportItem -> {
                        ReportMergePrintInfoDto.ReportItemResult reportItemResult =
                                new ReportMergePrintInfoDto.ReportItemResult();
                        reportItemResult.setReportItemCode(reportItem.getReportItemCode());
                        reportItemResult.setReportItemName(reportItem.getReportItemName());
                        reportItemResult.setResult(reportItem.getResult());
                        reportItemResult.setRange(reportItem.getRange());
                        reportItemResult.setJudge(reportItem.getJudge());
                        reportItemResult.setUnit(reportItem.getUnit());
                        reportItemResult.setInstrumentId(reportItem.getInstrumentId());
                        reportItemCodes.add(reportItemResult.getReportItemCode());
                        instrumentIds.add(reportItemResult.getInstrumentId());
                        reportItemResulList.add(reportItemResult);
                    });
                }
                result.setReportItemResulList(reportItemResulList);
                result.setResultType(resultType);
                result.setResult(resultValue);
                groupResultList.add(result);
            }
            temp.setGroupResultList(groupResultList);
            targetList.add(temp);
        }

        // 避免 展示空白 页
        if (CollectionUtils.isEmpty(targetList)) {
            targetList.add(new ReportMergePrintInfoDto());
        }
        // 仪器报告项目
        Map<String, InstrumentReportItemDto> instrumentReportItemByKey = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reportItemCodes) && CollectionUtils.isNotEmpty(instrumentIds)) {
            List<InstrumentReportItemDto> instrumentReportItemDtos =
                    instrumentReportItemService.selectByInstrumentIdsAndReportItemCodes(instrumentIds, reportItemCodes);
            instrumentReportItemByKey.putAll(instrumentReportItemDtos.stream().collect(
                    Collectors.toMap(obj -> obj.getInstrumentId() + "-" + obj.getReportItemCode(), Function.identity())));
        }

        // 拼接报告项目信息
        targetList.forEach(item -> {
            item.getGroupResultList().forEach(groupResult -> {
                if (CollectionUtils.isEmpty(groupResult.getReportItemResulList())) {
                    return;
                }
                groupResult.getReportItemResulList().forEach(obj -> {
                    // 优先取结果上 仪器
                    InstrumentReportItemDto instrumentReportItemDto =
                            instrumentReportItemByKey.get(obj.getInstrumentId() + "-" + obj.getReportItemCode());
                    if (Objects.isNull(instrumentReportItemDto)) {
                        instrumentReportItemDto = instrumentReportItemByKey
                                .get(groupResult.getInstrumentId() + "-" + obj.getReportItemCode());
                    }
                    if (Objects.nonNull(instrumentReportItemDto)) {
                        obj.setEnName(instrumentReportItemDto.getEnName());
                        obj.setEnAb(instrumentReportItemDto.getEnAb());
                        obj.setAliasName(instrumentReportItemDto.getAliasName());
                        obj.setExamMethodName(instrumentReportItemDto.getExamMethodName());
                        obj.setIsPrint(instrumentReportItemDto.getIsPrint());
                    }
                });

                List<ReportMergePrintInfoDto.ReportItemResult> reportItemResulList = groupResult.getReportItemResulList();
                groupResult.setReportItemResulList(reportItemResulList.stream()
                        // 找不到 是否打印 配置的默认展示出来，在报告单模板里面做判断
                        .filter(e -> Objects.isNull(e.getIsPrint()) || Objects.equals(YesOrNoEnum.YES.getCode(), e.getIsPrint()))
                        .collect(Collectors.toList()));

            });
        });

        return targetList.stream()
                .flatMap(item -> {
                    if (forcedMergeHspOrgCodeList.contains(hspOrgCode)) {
                        return item.getReportUrls().stream();
                    }

                    final PdfReportParamDto param = new PdfReportParamDto();
                    param.put("target", Dict.parse(item));

                    log.info("报告单合并打印 参数 [{}]", JSON.toJSONString(param));

                    return Stream.of(pdfReportService.build2Url(PdfTemplateTypeEnum.REPORT_MERGE_PRINT_INFO.getCode(), param, 3));
                })
                .collect(Collectors.toList());
    }


    // 多张报告单合并成一张报告单
    @Override
    public String mergePdfs(List<String> pdfUrls, String barcode) {

        // 地址为空，不进行合并
        if (CollectionUtils.isEmpty(pdfUrls)) {
            return StringUtils.EMPTY;
        }

        File tempFile = FileUtil.createTempFile(MERGE_TEMP_DIR, null, null, true);

        // 合并新地址成一个pdf
        List<File> files = new ArrayList<>();
        final PDFMergerUtility merger = new PDFMergerUtility();
        try {
            for (String pdfUrl : pdfUrls) {
                merger.addSource(huaweiObsUtils.downloadFileByUrl(pdfUrl));
            }
        } catch (Exception e) {
            log.error("pdf合并成一个url下载失败！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 下载失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        }

        final PDDocumentInformation information = new PDDocumentInformation();
        // information.setKeywords("");
        merger.setDestinationFileName(tempFile.getAbsolutePath());
        merger.setDestinationDocumentInformation(information);

        try {
            merger.mergeDocuments(MemoryUsageSetting.setupTempFileOnly());
        } catch (Exception e) {
            log.error("pdf合并成一个url合成失败，返回多个pdf文件！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 合成失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        } finally {
            files.forEach(FileUtils::deleteQuietly);
        }

        try {
            // 上传文件到obs
            // String objectKey = MERGE_FILE_PREFIX + LocalDateTime.now().format(DateTimeFormatter.ofPattern("/yyyy/MM/dd/")) + UUID.randomUUID() + PDF_SUFFIX;
            return huaweiObsUtils.upload(new FileInputStream(tempFile), MediaType.APPLICATION_PDF_VALUE);
        } catch (Exception e) {
            log.error("pdf合并成一个url上传失败，返回多个pdf文件！外部条码号：{}",barcode, e);
            throw new IllegalArgumentException("pdf合并成一个url时 上传失败！外部条码号：" + barcode + " 异常信息：" + e.getMessage());
        } finally {
            FileUtils.deleteQuietly(tempFile);
        }

    }


}
