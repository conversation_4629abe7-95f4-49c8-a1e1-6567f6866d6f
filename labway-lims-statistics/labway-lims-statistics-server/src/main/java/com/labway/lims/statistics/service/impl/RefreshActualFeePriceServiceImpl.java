package com.labway.lims.statistics.service.impl;

import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.labway.lims.api.RedisDistributedLock;
import com.labway.lims.api.RedisPrefix;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplyPageDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.QueryApplyPageDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.statistics.api.client.RefreshActualFeePriceService;
import com.labway.lims.statistics.dto.RefreshActualFeePriceLog;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class RefreshActualFeePriceServiceImpl implements RefreshActualFeePriceService {

    @Resource
    private RedisPrefix redisPrefix;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private ApplySampleService applySampleService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private ItemPriceBasePackageService itemPriceBasePackageService;

    /**
     * 查询申请单数量
     */
    private static final long pageSize = 500;

    /**
     * 刷新实际收费价格锁
     */
    private static final String REFRESH_ACTUAL_FEE_PRICE_KEY = "%s:refreshActualFeePrice:";
    /**
     * 刷新价格进度
     */
    private static final String REFRESH_ACTUAL_FEE_PRICE_LOG_KEY = REFRESH_ACTUAL_FEE_PRICE_KEY + "list:";


    // Lua脚本：释放锁
    private static final String UNLOCK_SCRIPT =
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                    "    return redis.call('del', KEYS[1]) " +
                    "else " +
                    "    return 0 " +
                    "end";

    // Lua脚本：续期锁
    private static final String RENEW_SCRIPT =
            "if redis.call('get', KEYS[1]) == ARGV[1] then " +
                    "    return redis.call('pexpire', KEYS[1], ARGV[2]) " +
                    "else " +
                    "    return 0 " +
                    "end";


    @AllArgsConstructor
    @Getter
    private enum LogType {
        ERROR("刷新实际收费价格失败"),
        NULL_HSP_ORG("没有需要处理的送检机构"),

        START("开始进行统计"),
        END("财务计算结束"),
        APPLY_COUNT("申请单数量:"),
        APPLY_BATCH("当前已处理数量：%s, 总数量：%s"),
        ;
        private final String log;


    }

    @Override
    public List<Object> refreshActualFeePrice(Date startDate, Date endDate, Set<Long> hspOrgIds) {
        // 限流key
        final String refreshActualFeePriceKey = getRefreshActualFeePriceKey();
        // 进度key
        final String refreshActualFeePriceLogKey = getRefreshActualFeePriceLogKey();

        // 实现看门狗
        final RedisDistributedLock redisDistributedLock = new RedisDistributedLock(stringRedisTemplate, refreshActualFeePriceKey, 1000 * 60 * 10);
        if (redisDistributedLock.tryLock()) {
            return getRefreshActualFeePriceLogs(refreshActualFeePriceLogKey);
        }
        try {

            // 删除上一个进度key
            stringRedisTemplate.delete(refreshActualFeePriceLogKey);

            StopWatch stopWatch = new StopWatch("刷新实际收费价格");
            stopWatch.start();
            final List<HspOrganizationDto> hspOrganizationDtos = selectHspOrganizationDto(hspOrgIds);
            if (CollectionUtils.isEmpty(hspOrganizationDtos)) {
                return Collections.singletonList(new RefreshActualFeePriceLog(new HspOrganizationDto(), LogType.NULL_HSP_ORG.log));
            }

            for (HspOrganizationDto hspOrganizationDto : hspOrganizationDtos) {

                // 时间统计
                StopWatch hspOrgStopWatch = new StopWatch(hspOrganizationDto.getHspOrgName());
                hspOrgStopWatch.start();

                final QueryApplyPageDto queryApplyPageDto = QueryApplyPageDto.builder()
                        .hspOrgId(hspOrganizationDto.getHspOrgId())
                        .createDateStart(startDate)
                        .createDateEnd(endDate)
                        .size(pageSize)
                        .build();

                // 开始日志
                printLog(refreshActualFeePriceLogKey, hspOrganizationDto, LogType.START.log);

                // 当前机构申请单数量
                final long count = applyService.selectPageCount(queryApplyPageDto);
                printLog(refreshActualFeePriceLogKey, hspOrganizationDto, LogType.APPLY_COUNT.log + count);
                if (count < 1) {
                    hspOrgStopWatch.stop();
                    printLog(refreshActualFeePriceLogKey, hspOrganizationDto, LogType.END.log + "--------耗时(s)：" + hspOrgStopWatch.getLastTaskTimeMillis() / 1000);
                    continue;
                }

                // 可查询 页数
                long pageCount = count / pageSize + 1;

                try {
                    long applySize = 0;
                    // 开始循环处理
                    for (long i = 1; i <= pageCount; i++) {
                        queryApplyPageDto.setCurrent(i);
                        final ApplyPageDto applyPageDto = applyService.selectPageData(queryApplyPageDto);
                        final List<ApplyDto> applys = applyPageDto.getApplys();
                        refreshByApplyDtoList(applys);
                        // 进度日志
                        printLog(refreshActualFeePriceLogKey, hspOrganizationDto, String.format(LogType.APPLY_BATCH.log, applySize += applys.size(), count));
                    }
                    // 当前机构结束日志
                    printLog(refreshActualFeePriceLogKey, hspOrganizationDto, LogType.END.log);

                } catch (Exception e) {
                    log.error(LogType.ERROR.log, e);
                    printLog(refreshActualFeePriceLogKey, hspOrganizationDto, e.toString());
                }
                hspOrgStopWatch.stop();
                printLog(refreshActualFeePriceLogKey, hspOrganizationDto, LogType.END.log + "--------耗时(s)：" + hspOrgStopWatch.getLastTaskTimeMillis() / 1000);
            }

            stopWatch.stop();
            printLog(refreshActualFeePriceLogKey, new HspOrganizationDto(), LogType.END.log + "--------总耗时(s)：" + stopWatch.getLastTaskTimeMillis() / 1000);
        } finally {
            // 延迟10秒删除
            stringRedisTemplate.expire(refreshActualFeePriceKey, Duration.ofSeconds(10));
            stringRedisTemplate.expire(refreshActualFeePriceLogKey, Duration.ofSeconds(10));
        }
        return getRefreshActualFeePriceLogs(refreshActualFeePriceLogKey);
    }

    private String getRefreshActualFeePriceKey() {
        return String.format(REFRESH_ACTUAL_FEE_PRICE_KEY, redisPrefix.getBasePrefix());
    }

    private String getRefreshActualFeePriceLogKey() {
        return String.format(REFRESH_ACTUAL_FEE_PRICE_LOG_KEY, redisPrefix.getBasePrefix());
    }


    private void printLog(String refreshActualFeePriceLogKey, HspOrganizationDto hspOrganizationDto, String logMsg) {
        log.info("[{}] {}", hspOrganizationDto.getHspOrgName(), logMsg);
        stringRedisTemplate.opsForList().rightPush(refreshActualFeePriceLogKey, new RefreshActualFeePriceLog(hspOrganizationDto, logMsg).toJSON());
    }

    private List<Object> getRefreshActualFeePriceLogs(String refreshActualFeePriceLogKey) {
        final List<String> logs = stringRedisTemplate.opsForList().range(refreshActualFeePriceLogKey, 0, -1);
        if (CollectionUtils.isEmpty(logs)) {
            return Collections.emptyList();
        }
        return logs.stream()
                .map(JSON::parse)
                .collect(Collectors.toList());
    }

    /**
     * 根据送检机构查询， 如果没有送检机构， 则查询所有送检机构
     */
    private List<HspOrganizationDto> selectHspOrganizationDto(Set<Long> hspOrgIds) {
        if (CollectionUtils.isEmpty(hspOrgIds)) {
            return hspOrganizationService.selectAll();
        }
        return hspOrganizationService.selectByHspOrgIds(hspOrgIds);
    }


    /**
     * 根据 申请单 刷新 其下的 实际收费价格
     */
    public void refreshByApplyDtoList(List<ApplyDto> applyDtos) {
        if (CollectionUtils.isEmpty(applyDtos)) {
            return;
        }
        Set<Long> applyIds = applyDtos.stream().map(ApplyDto::getApplyId).collect(Collectors.toSet());

        List<ApplySampleDto> applySampleDtos = applySampleService.selectByApplyIds(applyIds);
        List<Long> applySampleIds =
                applySampleDtos.stream().map(ApplySampleDto::getApplySampleId).collect(Collectors.toList());

        // key: 申请单id value: 申请单样本
        Map<Long, List<ApplySampleDto>> applySampleByApplyId =
                applySampleDtos.stream().collect(Collectors.groupingBy(ApplySampleDto::getApplyId));

        // key: 申请单样本id value: 申请单样本检验项目
        Map<Long, List<ApplySampleItemDto>> sampleItemByApplySampleId =
                applySampleItemService.selectByApplySampleIds(applySampleIds).stream()
                        .collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));

        // 需要更新实际收费价格的
        List<ApplySampleItemDto> updateList = Lists.newArrayList();
        for (ApplyDto applyDto : applyDtos) {
            List<ApplySampleDto> sampleDtoList = applySampleByApplyId.get(applyDto.getApplyId());
            if (CollectionUtils.isEmpty(sampleDtoList)) {
                continue;
            }
            for (ApplySampleDto applySampleDto : sampleDtoList) {
                List<ApplySampleItemDto> sampleItemDtoList =
                        sampleItemByApplySampleId.get(applySampleDto.getApplySampleId());
                if (CollectionUtils.isEmpty(sampleItemDtoList)) {
                    continue;
                }
                for (ApplySampleItemDto sampleItemDto : sampleItemDtoList) {

                    Map<Long,
                            BigDecimal> actualFeePriceByTestItemId = itemPriceBasePackageService.selectActualFeePrice(
                            applyDto.getHspOrgId(), applyDto.getApplyTypeCode(), sampleItemDto.getCreateDate(),
                            Collections.singleton(sampleItemDto.getTestItemId()));
                    // 未匹配到实际 收费价格
                    BigDecimal actualFeePrice = actualFeePriceByTestItemId.get(sampleItemDto.getTestItemId());
                    if (Objects.isNull(actualFeePrice)) {
                        continue;
                    }
                    // 与现有的保持一致
                    if (actualFeePrice.compareTo(sampleItemDto.getActualFeePrice()) == 0) {
                        continue;
                    }
                    ApplySampleItemDto update = new ApplySampleItemDto();
                    update.setApplySampleItemId(sampleItemDto.getApplySampleItemId());
                    update.setActualFeePrice(actualFeePrice);
                    updateList.add(update);
                    log.info("需要调整：barcode{},testItemName:{},oldActualFeePrice:{}, actualFeePrice:{}",
                            applySampleDto.getBarcode(), sampleItemDto.getTestItemName(), sampleItemDto.getActualFeePrice(),
                            actualFeePrice);
                }
            }
        }
        if (CollectionUtils.isEmpty(updateList)) {
            log.info("本轮为空");
            return;
        }
        // 更新
        applySampleItemService.updateBatchById(updateList);
    }
}
