package com.labway.lims.statistics.dto;

import com.alibaba.fastjson.JSON;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public class RefreshActualFeePriceLog implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long hspOrgId;
    private String hspOrgName;
    private Date date;
    private String log;

    public RefreshActualFeePriceLog(HspOrganizationDto hspOrganizationDto, String log) {
        this.hspOrgId = hspOrganizationDto.getHspOrgId();
        this.hspOrgName = hspOrganizationDto.getHspOrgName();
        this.date = new Date();
        this.log = log;
    }

    public String toJSON() {
        return JSON.toJSONString(this);
    }

}