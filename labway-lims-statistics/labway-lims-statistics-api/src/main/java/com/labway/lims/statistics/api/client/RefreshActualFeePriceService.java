package com.labway.lims.statistics.api.client;


import com.labway.lims.apply.api.dto.ApplyDto;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface RefreshActualFeePriceService {

    /**
     * 刷新价格
     */
    List<Object> refreshActualFeePrice(Date startDate, Date endDate, Set<Long> hspOrgIds);

    /**
     * 刷新这些申请单的价格
     */
    void refreshByApplyDtoList(List<ApplyDto> applyDtos);
}
