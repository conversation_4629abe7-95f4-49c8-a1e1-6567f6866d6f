package com.labway.lims.statistics.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * 刷新 tb_apply_sample_item 实际收费价格 actual_fee_price
 */
@Data
public class RefreshActualFeePriceDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Date startDate;

    private Date endDate;

    private Set<Long> hspOrgIds;
}
