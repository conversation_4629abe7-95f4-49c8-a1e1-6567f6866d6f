package com.labway.lims.routine.api.service;

import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplyUpdateBeforeCheckTipDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.routine.api.dto.QuerySampleDto;
import com.labway.lims.routine.api.dto.RoutineSampleTwoUnPickInfoDto;
import com.labway.lims.routine.api.dto.SampleAuditDto;
import com.labway.lims.routine.api.dto.SampleCancelAuditDto;
import com.labway.lims.routine.api.dto.SampleDto;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.time.LocalDate;
import java.util.Collection;
import java.util.Date;
import java.util.List;

public interface SampleService {
    /**
     * 根据ID查询
     */
    @Nullable
    SampleDto selectBySampleId(long sampleId);

    ApplyUpdateBeforeCheckTipDto updateCheckExceptionAndCritical(List<ApplySampleDto> applySampleDtos, ApplyDto applyDto);

    @Deprecated
    long insertTest();

    /**
     * 根据检验日期查询当前专业组下的样本
     * <p>
     * 提示⚠️：这个方法在仪器接收器那里使用到了，请避免在内部调用 {@link LoginUserHandler#get()}
     *
     * @return List<SampleDto>
     */
    @Nonnull
    List<SampleDto> selectByTestDate(QuerySampleDto dto);

    /**
     * 查询样本列表
     */
    List<SampleDto> selectSamples(QuerySampleDto dto);

    /**
     * 查询当前专业组下的样本
     *
     * @param groupId groupId
     * @return List<SampleDto>
     */
    @Nonnull
    List<SampleDto> selectByGroupId(long groupId);

    /**
     * 保存样本
     *
     * @param dto SampleDto
     */
    boolean updateBySampleId(SampleDto dto);

    /**
     * 根据applySampleId查询
     *
     * @param applySampleId applySampleId
     * @return SampleDto
     */
    @Nullable
    SampleDto selectByApplySampleId(long applySampleId);

    /**
     * 根据applySampleId查询
     */
    List<SampleDto> selectByApplySampleIds(Collection<Long> applySampleIds);

    /**
     * 根据applyId查询样本
     *
     * @param applyId applyId
     * @return List<SampleDto>
     */
    List<SampleDto> selectByApplyId(long applyId);

    /**
     * 根据样本ids 查看样本结果
     */
    List<SampleDto> selectBySampleIds(Collection<Long> sampleIds);

    /**
     * 审核样本
     *
     * @param dto SampleAuditDto
     */
    void auditSamplesChain(SampleAuditDto dto);

    /**
     * 取消一审
     *
     * @param dto SampleAuditDto
     */
    void cancelOneAuditSample(SampleCancelAuditDto dto);

    /**
     * 取消二审
     *
     * @param dto SampleAuditDto
     */
    void cancelTwoAuditSample(SampleCancelAuditDto dto);

    /**
     * 二次分拣
     */
    long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, long instrumentId);

    /**
     * 免疫二次分拣
     */
    long twoPick(long applySampleId, long instrumentGroupId, String sampleNo, long instrumentId, Date immunityTwoPickDate);

    /**
     * 取消二次分拣
     *
     * @return 样本号
     */
    RoutineSampleTwoUnPickInfoDto twoUnPick(Collection<Long> applySampleIds);

    /**
     * 添加样本
     */
    long addSample(SampleDto sample);

    /**
     * 删除样本
     */
    boolean deleteBySampleId(long sampleId);

    /**
     * 删除样本
     */
    void deleteBySampleIds(Collection<Long> sampleIds);

    List<SampleDto> selectByApplyIds(Collection<Long> applyIds);

    /**
     * 重新生成报告
     *
     * @return
     */
    SampleReportDto rebuildReport(long applySampleId);

    /**
     * 命中多个时返回一个
     */
    @Nullable
    SampleDto selectByBarcode(String barcode);

    /**
     * 命中多个时返回所有
     */
    List<SampleDto> selectAllByBarcode(String barcode);

    /**
     * 根据条码号批量查询，命中多个时返回所有
     */
    List<SampleDto> selectAllByBarcodes(List<String> barcodes);

    /**
     * 根据创建时间查询
     *
     * @param groupId 可以为空
     */
    List<SampleDto> selectByCreateDate(Date beginCreateDate, Date endCreateDate, @Nullable Long groupId);

    /**
     * 根据ID修改
     */
    void updateBySampleIds(SampleDto sampleDto, Collection<Long> sampleIds);

    /**
     * 根据样本号s 查询样本信息
     * @param sampleNos
     * @return
     */
    List<SampleDto> selectBySampleNos(Collection<String> sampleNos, LocalDate testDate, long groupId);

    /**
     * 根据样本号s 查询样本信息
     * @param sampleNo
     * @return
     */
    SampleDto selectBySampleNo(String sampleNo, LocalDate testDate);


    void updateByApplyId(SampleDto sampleDto);

    void updateByApplyIds(SampleDto sampleDto, Collection<Long> applyIds);

    /**
     * 查询包含已删除的
     * @param dto
     * @return
     */
    List<SampleDto> selectAllByTestDate(QuerySampleDto dto);
}
