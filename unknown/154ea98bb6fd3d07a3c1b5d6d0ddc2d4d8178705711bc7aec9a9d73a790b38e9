package com.labway.lims.outsourcing.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.labway.lims.api.enums.apply.PatientSubAgeUnitEnum;
import com.labway.lims.api.enums.apply.SexEnum;
import com.labway.lims.api.enums.apply.UrgentEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2023/4/10 16:12
 */
@Setter
@Getter
public class OutsourcingRoutineSamplesVo {

    /**
     * ID
     */
    private Long outsourcingSampleId;

    /**
     * 样本申请单ID
     */
    private Long applySampleId;

    /**
     * 申请单ID
     */
    private Long applyId;

    /**
     * 样本状态
     */
    private Integer status;

    /**
     * 条码号
     */
    private String barcode;

    /**
     * 样本号
     */
    private String sampleNo;

    /**
     * 专业组ID
     */
    private Long groupId;

    /**
     * 专业组名称
     */
    private String groupName;

    /**
     * 专业小组
     */
    private Long instrumentGroupId;

    /**
     * 专业小组名称
     */
    private String instrumentGroupName;


    /**
     * 检验日期，暂定二次分拣日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date testDate;


    /**
     * 一审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date checkDate;

    /**
     * 二次审核人工号
     */
    private String checkerName;
    /**
     * 二次审核
     */
    private Long checkerId;

    /**
     * 录入时间（申请单创建时间）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date enterDate;

    /**
     * 终审时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Date finalCheckDate;

    /**
     * 样本备注
     */
    private String sampleRemark;

    /**
     * 结果备注
     */
    private String resultRemark;

    /**
     * 送检机构ID
     */
    private Long hspOrgId;

    /**
     * 送检机构
     */
    private String hspOrgName;

    /**
     * 样本是否加急 (1急诊 0不急)
     *
     * @see UrgentEnum
     */
    private Integer urgent;

    /**
     * 检验项目名称
     */
    private String testItemName;

    /**
     * 病人姓名
     */
    private String patientName;
    /**
     * 性别 1 男，2:女
     *
     * @see SexEnum
     */
    private String patientSex;
    /**
     * 年龄
     */
    private Integer patientAge;
    /**
     * 子年龄
     */
    private Integer patientSubage;
    /**
     * 月、周、天
     *
     * @see PatientSubAgeUnitEnum
     */
    private String patientSubageUnit;
    /**
     * 是否打印
     */
    private Integer isPrint;

    /**
     * 就诊类型
     */
    private String applyType;

    /**
     * 临床诊断
     */
    private String diagnosis;

}
