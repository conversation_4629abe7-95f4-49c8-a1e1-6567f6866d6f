package com.labway.lims.job.handler.xxl.statistics;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.labway.lims.apply.api.dto.ApplyPageDto;
import com.labway.lims.apply.api.dto.QueryApplyPageDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.service.ItemPriceBasePackageService;
import com.labway.lims.statistics.api.client.RefreshActualFeePriceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;

/**
 * 刷新 tb_apply_sample_item 实际收费价格 actual_fee_price
 * 
 * <AUTHOR>
 * @since 2023/9/26 9:58
 */
@Slf4j
@Component
public class RefreshActualFeePrice {

    @DubboReference
    private ApplyService applyService;
    @DubboReference
    private RefreshActualFeePriceService refreshActualFeePriceService;

    /**
     * 刷新 实际收费价格
     */
    @XxlJob("refreshActualFeePrice")
    public void refreshActualFeePrice() {
        String param = XxlJobHelper.getJobParam();
        QueryApplyPageDto jobParamDto = new QueryApplyPageDto();
        try {
            jobParamDto = JSONUtil.toBean(param, QueryApplyPageDto.class);
        } catch (Exception e) {
            XxlJobHelper.log("输入参数不规范{}", param);
        }

        if (Objects.isNull(jobParamDto.getCreateDateStart()) || Objects.isNull(jobParamDto.getCreateDateEnd())) {
            // 默认 当天 所在上个月
            LocalDate localDate = LocalDate.now().minusMonths(1).withDayOfMonth(1);
            jobParamDto
                .setCreateDateStart(Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
            jobParamDto.setCreateDateEnd(new Date());
        }

        execute(jobParamDto);
    }

    public void execute(QueryApplyPageDto pageDto) {
        Long current = 1L;
        Long size = 2000L;

        pageDto.setSize(size);
        pageDto.setCurrent(current);
        log.info("查询参数:{}", JSON.toJSONString(pageDto));
        ApplyPageDto applyPageDto = applyService.selectPageData(pageDto);

        // 此 范围 下 申请单
        Long total = applyPageDto.getTotal();
        // 执行 第一轮
        log.info("执行 total :{} , current :{}, count :{}", total, current, applyPageDto.getApplys().size());
        refreshActualFeePriceService.refreshByApplyDtoList(applyPageDto.getApplys());

        // 可查询 页数
        long pageCount = total / size + 1;

        while (current < pageCount) {
            current++;

            pageDto.setCurrent(current);
            ApplyPageDto pageData = applyService.selectPageData(pageDto);

            refreshActualFeePriceService.refreshByApplyDtoList(pageData.getApplys());
            log.info("执行 total :{} , current :{}, count :{}", total, current, pageData.getApplys().size());
        }
    }

}
