package com.labway.lims.apply.service;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.labway.business.center.compare.request.LimsCancelHandSampleRequest;
import com.labway.business.center.compare.request.LimsPushHandFormRequest;
import com.labway.business.center.compare.service.OutApplyInfoService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySourceEnum;
import com.labway.lims.api.enums.apply.ApplyStatusEnum;
import com.labway.lims.api.enums.apply.BarcodeFlowEnum;
import com.labway.lims.api.enums.apply.PushSampleTypeEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.apply.api.DiffApplyDto;
import com.labway.lims.apply.api.dto.*;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplySampleService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.apply.api.service.SampleFlowService;
import com.labway.lims.apply.config.BusinessConfig;
import com.labway.lims.apply.mapper.TbApplyMapper;
import com.labway.lims.apply.model.TbApply;
import com.labway.lims.apply.service.chain.apply.add.AddApplyChain;
import com.labway.lims.apply.service.chain.apply.add.AddApplyContext;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyChain;
import com.labway.lims.apply.service.chain.apply.update.UpdateApplyContext;
import com.labway.lims.apply.service.chain.apply.update.UpdateFillApplyAndDiffInfoCommand;
import com.labway.lims.apply.service.chain.apply.update.UpdateFlowCommand;
import com.labway.lims.apply.service.chain.apply.update.UpdateHspOrgDeptOrDoctorCommand;
import com.labway.lims.apply.service.chain.apply.update.batch.BatchUpdateApplyChain;
import com.labway.lims.apply.service.chain.apply.update.batch.BatchUpdateApplyContext;
import com.labway.lims.apply.service.chain.apply.update.sample.UpdateApplySampleChain;
import com.labway.lims.base.api.dto.HspOrganizationDto;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.swak.frame.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.builder.DiffResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@CacheConfig(cacheNames = "apply")
public class ApplyServiceImpl extends ServiceImpl<TbApplyMapper, TbApply> implements ApplyService {

    @Resource
    private TbApplyMapper tbApplyMapper;

    @Resource
    private ApplySampleService applySampleService;

    @Resource
    private ApplySampleItemService applySampleItemService;

    @Resource
    private AddApplyChain addApplyChain;

    @Resource
    private UpdateApplyChain updateApplyChain;

    @Resource
    private UpdateApplySampleChain updateApplySampleChain;

    @Resource
    private BatchUpdateApplyChain batchUpdateApplyChain;

    @DubboReference
    private HspOrganizationService hspOrganizationService;

    @Resource
    private SampleFlowService sampleFlowService;
    @Resource
    private UpdateHspOrgDeptOrDoctorCommand updateHspOrgDeptOrDoctorCommand;

    @Resource
    private OutApplyInfoService outApplyInfoService;

    @Resource
    private BusinessConfig businessConfig;


    @Nullable
    @Override
    public ApplyDto selectByApplyId(long applyId) {
        return convert(tbApplyMapper.selectById(applyId));
    }

    @Override
    public List<ApplyDto> selectByApplyIds(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyList();
        }
        return tbApplyMapper.selectList(new LambdaQueryWrapper<TbApply>().in(TbApply::getApplyId, applyIds)).stream()
                .map(this::convert).collect(Collectors.toList());
    }

    @Nonnull
    @Override
    public List<ApplyDto> selectUnreviewedApplySampleByQuery(String applySource, Long hspOrgId) {
        if (StringUtils.isBlank(applySource)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbApply> in =
                Wrappers.lambdaQuery(TbApply.class).eq(Objects.nonNull(hspOrgId), TbApply::getHspOrgId, hspOrgId)
                        .eq(TbApply::getSource, applySource).in(TbApply::getStatus,
                                List.of(ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode(), ApplyStatusEnum.WAIT_CHECK.getCode()));
        return tbApplyMapper.selectList(in).stream().map(this::convert).collect(Collectors.toList());

    }

    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApplyInfo addApply(TestApplyDto addApply) {
        final LoginUserHandler.User user = LoginUserHandler.get();

        AddApplyContext context = new AddApplyContext();
        context.setTestApply(addApply);
        context.setUser(user);

        try {
            if (!addApplyChain.execute(context)) {
                throw new IllegalStateException("添加申请单失败");
            }
            final String masterBarcode = context.getApply().getMasterBarcode();
            log.info("新建申请单成功 专业组 [{}] 操作人 [{}] 主条码 [{}}", user.getGroupName(), user.getNickname(), masterBarcode);

            return context.getApplyInfo();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            log.error("新建申请单失败 专业组 [{}] 操作人 [{}]", user.getGroupName(), user.getNickname(), e);
            throw new IllegalStateException(e.getMessage(), e);
        } finally {
            log.info("用户 [{}] 新增申请单耗时 \n{}", user.getNickname(), context.getWatch().prettyPrint(TimeUnit.MILLISECONDS));
        }

    }

    @CacheEvict(allEntries = true)
    @Override
    public void add(ApplyDto applyDto) {
        tbApplyMapper.insert(convert(applyDto));
    }

    @CacheEvict(allEntries = true)
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public boolean updateByApplyId(ApplyDto apply) {
        if (Objects.isNull(apply) || Objects.isNull(apply.getApplyId())) {
            return false;
        }
        LoginUserHandler.User user = LoginUserHandler.get();
        TbApply tbApply = JSON.parseObject(JSON.toJSONString(apply), TbApply.class);
        tbApply.setUpdateDate(new Date());
        tbApply.setUpdaterId(user.getUserId());
        tbApply.setUpdaterName(user.getNickname());
        return tbApplyMapper.updateById(tbApply) > 0;
    }

    @CacheEvict(allEntries = true)
    @Override
    public boolean deleteByApplyId(long applyId) {
        return deleteByApplyIds(List.of(applyId));
    }

    @Override
    public List<ApplyDto> selectApplyDoubleCheckList(Long hspOrgId, Date startDate, Date endDate) {
        if (Objects.isNull(hspOrgId) || Objects.isNull(startDate) || Objects.isNull(endDate)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbApply> eq =
            Wrappers.lambdaQuery(TbApply.class).eq(TbApply::getHspOrgId, hspOrgId).ge(TbApply::getCreateDate, startDate)
                .le(TbApply::getCreateDate, endDate).eq(TbApply::getStatus, ApplyStatusEnum.WAIT_DOUBLE_CHECK.getCode())
                .eq(TbApply::getSource, ApplySourceEnum.MANUAL.name());
        final List<TbApply> tbApplies = tbApplyMapper.selectList(eq);

        return tbApplies.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public Collection<Long> selectByHspOrgIdAndPatientNameAndSex(ApplyRecordDto dto) {
        final LambdaQueryWrapper<TbApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.select(TbApply::getApplyId);
        wrapper.eq(TbApply::getHspOrgId, dto.getHspOrgId()).gt(TbApply::getCreateDate, dto.getDateStart())
            .le(TbApply::getCreateDate, dto.getDateEnd());
        if (!Objects.isNull(dto.getApplyId())){
            //剔除掉自身的申请单  一般用于申请单更新操作
            wrapper.ne(TbApply::getApplyId,dto.getApplyId());
        }

        if (StringUtils.isNotBlank(dto.getPatientCard())) {
            wrapper.eq(TbApply::getPatientCard, dto.getPatientCard());
        } else {
            //1.1.3新增需求 年龄差在3岁内算同一个人
            wrapper.eq(TbApply::getPatientName, dto.getPatientName()).eq(TbApply::getPatientSex, dto.getPatientSex());
            //年龄范围 左3
            int age = Objects.isNull(dto.getPatientAge())?0:dto.getPatientAge();
            wrapper.ge(TbApply::getPatientAge,age-3).le(TbApply::getPatientAge, age);
        }
        return tbApplyMapper.selectList(wrapper).stream().map(this::convert).map(ApplyDto::getApplyId)
            .collect(Collectors.toSet());
    }

    @CacheEvict(allEntries = true)
    @Override
    public ApplyInfo update(TestApplyDto update) {
        Long applyId = null;
        try {
            UpdateApplyContext context = new UpdateApplyContext();
            context.setTestApply(update);
            context.setBarcode(update.getBarcode());
            if (update instanceof UpdateTestApplyDto) {
                context.setBatchUpdateItem(((UpdateTestApplyDto) update).isBatchUpdateItem());
            }
            applyId = context.getApplyId();

            if (!updateApplyChain.execute(context)) {
                throw new IllegalStateException("更新申请单失败");
            }
            return context.getApplyInfo();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            log.error("更新申请单失败 专业组 [{}] 操作人 [{}] 申请单ID [{}]", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), applyId, e);
            throw new IllegalStateException(e.getMessage(), e);
        }

    }

    @Override
    public AddSamplesInfoDto updateConfirm(TestApplyDto update) {
        Long applyId = null;
        try {
            UpdateApplyContext context = new UpdateApplyContext();
            context.setTestApply(update);
            if (update instanceof UpdateTestApplySampleDto) {
                context.setBarcode(((UpdateTestApplySampleDto) update).getBarcode());
            }
            applyId = context.getApplyId();

            if (!updateApplyChain.execute(context)) {
                throw new IllegalStateException("更新申请单失败");
            }
            return context.getAddSamplesInfo();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            log.error("更新申请单失败 专业组 [{}] 操作人 [{}] 申请单ID [{}]", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), applyId, e);
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    @Override
    public List<ApplyDto> selectByDate(Date start, Date end) {
        if (Objects.isNull(start) || Objects.isNull(end)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbApply> eq = Wrappers.lambdaQuery(TbApply.class).ge(TbApply::getCreateDate, start)
                .le(TbApply::getCreateDate, end)
                .orderByAsc(TbApply::getCreateDate);

        return tbApplyMapper.selectList(eq).stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * 修改申请单和样本信息
     *
     * @param update
     * @return
     */
    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApplyInfo updateApplySample(TestApplyDto update) {
        Long applyId = null;
        try {
            UpdateApplyContext context = new UpdateApplyContext();
            context.setTestApply(update);
            applyId = context.getApplyId();

            if (!updateApplySampleChain.execute(context)) {
                throw new IllegalStateException("修改申请单和样本信息失败");
            }
            return context.getApplyInfo();
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }

            log.error("修改申请单和样本信息失败 专业组 [{}] 操作人 [{}] 申请单ID [{}]", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), applyId, e);
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateApply(BatchUpdateApplyDto dto) {
        try {
            BatchUpdateApplyContext context = new BatchUpdateApplyContext();
            context.setUser(LoginUserHandler.get());
            context.setBatchUpdateApplyDto(dto);
            if (!batchUpdateApplyChain.execute(context)) {
                throw new IllegalStateException("修改申请单和样本信息失败");
            }
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            log.error("批量修改申请单信息失败 专业组 [{}] 操作人 [{}] 申请单ID [{}]", LoginUserHandler.get().getGroupName(),
                    LoginUserHandler.get().getNickname(), dto.getApplyIds(), e);
            throw new IllegalStateException(e.getMessage(), e);
        }
    }

    @Override
    public List<ApplyDto> selectHspOrgSignList(HspOrgDateQueryDto queryDto) {
        if (Objects.isNull(queryDto.getHspOrgId())
                || Objects.isNull(queryDto.getStartDate())
                || Objects.isNull(queryDto.getEndDate())
                || Objects.isNull(queryDto.getSource())) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbApply> eq =
                Wrappers.lambdaQuery(TbApply.class).eq(TbApply::getHspOrgId, queryDto.getHspOrgId()).ge(TbApply::getSignDate, queryDto.getStartDate())
                        .le(TbApply::getSignDate, queryDto.getEndDate()).eq(TbApply::getSource, queryDto.getSource().name())
                        .eq(StringUtils.isNotBlank(queryDto.getSigner()), TbApply::getCreatorName, queryDto.getSigner());

        return tbApplyMapper.selectList(eq).stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public List<ApplyDto> selectSignListBySignDate(Date start, Date end) {
        if (Objects.isNull(start) || Objects.isNull(end)) {
            return Collections.emptyList();
        }
        final LambdaQueryWrapper<TbApply> eq = Wrappers.lambdaQuery(TbApply.class).ge(TbApply::getSignDate, start)
                .le(TbApply::getSignDate, end).eq(TbApply::getSource, ApplySourceEnum.HIS.name());

        return tbApplyMapper.selectList(eq).stream().map(this::convert).collect(Collectors.toList());
    }

    @CacheEvict(allEntries = true)
    @Override
    public void updateByApplyIds(ApplyDto apply, List<Long> applyIds) {

        if (Objects.isNull(apply) || CollectionUtils.isEmpty(applyIds)) {
            return;
        }

        final LambdaQueryWrapper<TbApply> in = Wrappers.lambdaQuery(TbApply.class).in(TbApply::getApplyId, applyIds);

        tbApplyMapper.update(JSON.parseObject(JSON.toJSONString(apply), TbApply.class), in);
    }

    @CacheEvict(allEntries = true)
    @Override
    public void updateCancelCheckStatus(List<Long> applyIds) {

        if (CollectionUtils.isEmpty(applyIds)) {
            return;
        }

        final LoginUserHandler.User user = LoginUserHandler.get();

        final LambdaUpdateWrapper<TbApply> set = Wrappers.lambdaUpdate(TbApply.class);

        set.set(TbApply::getCheckDate, null);
        set.set(TbApply::getCheckerId, null);
        set.set(TbApply::getCheckerName, null);

        set.set(TbApply::getUpdateDate, new Date());
        set.set(TbApply::getUpdaterId, user.getUserId());
        set.set(TbApply::getUpdaterName, user.getNickname());

        set.set(TbApply::getStatus, ApplyStatusEnum.WAIT_CHECK.getCode());

        set.in(TbApply::getApplyId, applyIds);

        tbApplyMapper.update(null, set);
    }

    @Override
    public List<SampleApplyDto> selectUnreviewedApplySampleByQuery(UnreviewedApplyQueryDto query) {
        return tbApplyMapper.selectUnreviewedApplySampleByQuery(query);
    }

    @Override
    public Map<Long, ApplyDto> selectByApplyIdsAsMap(Collection<Long> applyIds) {
        return selectByApplyIds(applyIds).stream()
                .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public Map<Long, ApplyDto> selectAllByApplyIdsAndMergeAsMap(Collection<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.emptyMap();
        }
        List<TbApply> tbApplies = tbApplyMapper.selectAllByApplyIds(applyIds);
        return tbApplies.stream()
                .map(this::convert)
                .collect(Collectors.toMap(ApplyDto::getApplyId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public long selectPageCount(QueryApplyPageDto dto) {
        return super.count(pageQueryWrapper(dto));
    }

    @Override
    public ApplyPageDto selectPageData(QueryApplyPageDto dto) {

        final LambdaQueryWrapper<TbApply> wrapper = pageQueryWrapper(dto).orderByAsc(TbApply::getCreateDate);

        final Page<TbApply> page = new Page<>();
        page.setSize(dto.getSize());
        page.setCurrent(dto.getCurrent());
        final Page<TbApply> datas = tbApplyMapper.selectPage(page, wrapper);

        final ApplyPageDto pageApply = new ApplyPageDto();
        pageApply.setSize(datas.getSize());
        pageApply.setCurrent(datas.getCurrent());
        pageApply.setTotal(datas.getTotal());
        pageApply.setApplys(datas.getRecords().stream().map(this::convert).collect(Collectors.toList()));

        return pageApply;
    }

    private LambdaQueryWrapper<TbApply> pageQueryWrapper(QueryApplyPageDto dto) {
        final LambdaQueryWrapper<TbApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(dto.getOrgId()), TbApply::getOrgId, dto.getOrgId());
        wrapper.eq(Objects.nonNull(dto.getHspOrgId()), TbApply::getHspOrgId, dto.getHspOrgId());

        wrapper.in(CollectionUtils.isNotEmpty(dto.getMasterBarcodes()), TbApply::getMasterBarcode,
                dto.getMasterBarcodes());
        wrapper.ge(Objects.nonNull(dto.getCreateDateStart()), TbApply::getCreateDate, dto.getCreateDateStart());
        wrapper.le(Objects.nonNull(dto.getCreateDateEnd()), TbApply::getCreateDate, dto.getCreateDateEnd());
        return wrapper;
    }

    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteApply(Collection<Long> applyIds) {
        // 删除申请单
        deleteByApplyIds(applyIds);

        // 删除样本
        applySampleService.deleteByApplyIds(applyIds);

        // 检验项目
        applySampleItemService.deleteByApplyIds(applyIds);

    }

    @CacheEvict(allEntries = true)
    @Override
    public boolean deleteByApplyIds(Collection<Long> applyIds) {
        if (tbApplyMapper.deleteBatchIds(applyIds) < 1) {
            return false;
        }
        log.info("删除申请单成功 专业组 [{}] 操作人 [{}] 申请单ID [{}]", LoginUserHandler.get().getGroupName(),
                LoginUserHandler.get().getNickname(), applyIds);
        return true;
    }

    @CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public ApplyDto updateBasicApplyInfo(SimpleTestApplyDto testApply) {
        final ApplyDto apply = selectByApplyId(testApply.getApplyId());
        if (Objects.isNull(apply)) {
            throw new IllegalStateException("申请单不存在");
        }

        if (List.of(ApplyStatusEnum.CHECK.getCode(), ApplyStatusEnum.DOUBLE_CHECK.getCode())
                .contains(apply.getStatus())) {
            throw new IllegalStateException("申请单已复核，不能修改");
        }

        testApply.setStatus(apply.getStatus());

        final Long hspOrgId = testApply.getHspOrgId();
        if (Objects.isNull(hspOrgId)) {
            throw new IllegalArgumentException("请选择送检机构");
        }

        final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(hspOrgId);
        if (Objects.isNull(hspOrganization)) {
            throw new IllegalStateException("选择的送检机构为空");
        }
        if (!Objects.equals(hspOrganization.getEnable(), YesOrNoEnum.YES.getCode())) {
            throw new IllegalStateException(String.format("送检机构 [%s] 已停用", hspOrganization.getHspOrgName()));
        }

        final DiffApplyDto diffApplyDto =
                UpdateFillApplyAndDiffInfoCommand.fillApplyDiffInfo(testApply, hspOrganization);
        diffApplyDto.setApplyId(apply.getApplyId());

        final DiffResult<ApplyDto> diff = diffApplyDto.diff(apply);

        final String diffMsg = UpdateFillApplyAndDiffInfoCommand.getDiffMsg(diff);

        SpringUtil.getBean(ApplyService.class).updateByApplyId(diffApplyDto);

        // 更新外部条码号
        if (StringUtils.isNotBlank(testApply.getOutBarcode())) {
            LoginUserHandler.User user = LoginUserHandler.get();
            ApplySampleDto applySampleDto = new ApplySampleDto();
            applySampleDto.setOutBarcode(testApply.getOutBarcode());
            applySampleDto.setApplyId(apply.getApplyId());
            applySampleDto.setUpdateDate(new Date());
            applySampleDto.setUpdaterId(user.getUserId());
            applySampleDto.setUpdaterName(user.getNickname());
            applySampleService.updateByApplyId(applySampleDto);
        }

        final List<ApplySampleDto> applySamples = applySampleService.selectByApplyId(apply.getApplyId());
        final List<SampleFlowDto> sampleFlows = applySamples.stream()
                .map(m -> UpdateFlowCommand.getSampleFlow(m, diffMsg, BarcodeFlowEnum.APPLY_INFO_UPDATE))
                .collect(Collectors.toList());

        sampleFlowService.addSampleFlows(sampleFlows);

        // 记录条码环节
        return diffApplyDto;
    }

    @Override
    public List<ApplyDto> selectByMasterBarcodes(Set<String> masterBarcodes) {
        if (CollectionUtils.isEmpty(masterBarcodes)) {
            return Collections.emptyList();
        }

        final LambdaQueryWrapper<TbApply> in = Wrappers.lambdaQuery(TbApply.class)
                .eq(TbApply::getOrgId, LoginUserHandler.get().getOrgId()).in(TbApply::getMasterBarcode, masterBarcodes);

        return JSON.parseArray(JSON.toJSONString(tbApplyMapper.selectList(in)), ApplyDto.class);
    }

    /**
     * 推送病理到业务中台
     *
     * @param applyIds
     * @param applySampleIds
     */
    @Override
    public void sendPathologyToBusinessCenter(List<Long> applyIds, List<Long> applySampleIds) {
        String hspOrg = "";
        try {
            // 查询申请单
            Map<Long, ApplyDto> applyDtoMapById = selectByApplyIdsAsMap(applyIds);

            // 查询申请单样本
            List<ApplySampleDto> applySampleDtoList;
            if (CollectionUtils.isNotEmpty(applySampleIds)) {
                applySampleDtoList = applySampleService.selectByApplySampleIds(applySampleIds);
            } else {
                applySampleDtoList = applySampleService.selectByApplyIds(applyIds);
            }
            Map<Long, List<ApplySampleDto>> applySampleMapByApplySampleId =
                    applySampleDtoList.stream().collect(Collectors.groupingBy(ApplySampleDto::getApplySampleId));
            // 查询检验项目
            List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplyIds(applyIds);
            Map<Long, List<ApplySampleItemDto>> itemMapByApplySampleId =
                    applySampleItemDtos.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplySampleId));
            Map<Long, ApplyDto> applyMapByApplySampleId =
                    applySampleItemDtos.stream().collect(Collectors.toMap(ApplySampleItemDto::getApplySampleId,
                            applySampleItemDto -> applyDtoMapById.get(applySampleItemDto.getApplyId()), (a, b) -> a));

            // 查询出来项目类型为病理类型的项目
            Map<Long, List<ApplySampleItemDto>> applySampleItemMap = new HashMap<>();
            for (Map.Entry<Long, List<ApplySampleItemDto>> entry : itemMapByApplySampleId.entrySet()) {
                Long applySampleId = entry.getKey();
                // 取到病理的检验项目
                List<ApplySampleItemDto> pathologyItems =
                        entry.getValue().stream().filter(item -> ItemTypeEnum.PATHOLOGY.name().equals(item.getItemType()))
                                .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(pathologyItems)) {
                    applySampleItemMap.put(applySampleId, pathologyItems);
                }
            }

            Map<String, String> hspOrgs = applySampleDtoList.stream()
                    .collect(Collectors.toMap(ApplySampleDto::getHspOrgCode, ApplySampleDto::getHspOrgName, (v1, v2) -> v2));
            for (Map.Entry<String, String> hspOrgEntry : hspOrgs.entrySet()) {
                String hspOrgCode = hspOrgEntry.getKey();
                String hspOrgName = hspOrgEntry.getValue();
                hspOrg = hspOrgName;
                log.info("同步手工单到业务中台【机构编码：{}，机构名称：{}】", hspOrgCode, hspOrgName);

                LimsPushHandFormRequest limsPushHandFormRequest = new LimsPushHandFormRequest();
                List<LimsPushHandFormRequest.LimsPushHandFormSampleRequest> sampleRequests = new ArrayList<>();

                for (Map.Entry<Long, List<ApplySampleDto>> entry : applySampleMapByApplySampleId.entrySet()) {
                    Long applySampleId = entry.getKey();
                    // 申请单
                    ApplyDto applyDto = applyMapByApplySampleId.get(applySampleId);
                    if (!hspOrgCode.equals(applyDto.getHspOrgCode())) {
                        continue;
                    }
                    // 申请单样本
                    List<ApplySampleDto> applySampleDtos = entry.getValue();
                    // 检验项目
                    List<ApplySampleItemDto> itemDtos = applySampleItemMap.get(applySampleId);

                    if (CollectionUtils.isEmpty(applySampleDtos) || CollectionUtils.isEmpty(itemDtos)) {
                        log.error("同步手工单到业务中台，没有样本或检验项目【申请单ID：{}，机构名称：{}】", applySampleId, hspOrgName);
                        continue;
                    }

                    for (ApplySampleDto applySampleDto : applySampleDtos) {
                        sampleRequests.add(buildSampleRequest(applyDto, applySampleDto, itemDtos));
                    }

                    limsPushHandFormRequest.setHspOrgCode(applyDto.getHspOrgCode());
                    limsPushHandFormRequest.setHspOrgName(applyDto.getHspOrgName());
                    String source = applyDto.getSource();
                    // 手工单类型错误(1补录样本推送 2手工单样本推送)
                    Integer pushSampleType = ApplySourceEnum.SUPPLEMENTARY.name().equals(source)
                            ? PushSampleTypeEnum.LOGISTICS.getCode() : PushSampleTypeEnum.MANUAL.getCode();
                    limsPushHandFormRequest.setPushSampleType(pushSampleType);
                }

                if (CollectionUtils.isNotEmpty(sampleRequests)) {
                    String orgCode = LoginUserHandler.get().getOrgCode();
                    limsPushHandFormRequest.setOrgCode(businessConfig.getOrgCodeMap().getOrDefault(orgCode, orgCode));
                    for (LimsPushHandFormRequest.LimsPushHandFormSampleRequest sampleRequest : sampleRequests) {
                        sampleRequest.setSignOrgCode(limsPushHandFormRequest.getOrgCode());
                    }
                    limsPushHandFormRequest.setOrgName(LoginUserHandler.get().getOrgName());
                    limsPushHandFormRequest.setFormSource("LIMS_MANUAL");
                    limsPushHandFormRequest.setSendSampleCount(sampleRequests.size());
                    limsPushHandFormRequest.setSampleRequests(sampleRequests);
                    //调用人 信息
                    LoginUserHandler.User user = LoginUserHandler.get();
                    limsPushHandFormRequest.setOptUserId(String.valueOf(user.getUserId()));
                    limsPushHandFormRequest.setOptUserName(user.getNickname());

                    log.info("同步手工单到业务中台【参数：{}，机构名称：{}】", JSON.toJSONString(limsPushHandFormRequest), hspOrgName);
                    Response<?> response = outApplyInfoService.limsPushSample(limsPushHandFormRequest);
                    if (!response.isSuccess()) {
                        log.error("同步手工单到业务中台失败【{}，机构名称：{}】", response.getMsg(), hspOrgName);
                    }
                }
            }
        } catch (Exception e) {
            log.error("同步手工单到业务中台失败【机构名称：{}，ApplyId：{}，ApplySampleIds：{}，Error：{}】", hspOrg, JSON.toJSONString(applyIds),
                    JSON.toJSONString(applySampleIds), e.getMessage());
        }
    }

    /**
     * 取消推送病理到业务中台
     *
     * @param applyIds
     */
    @Override
    public void cancelSendPathologyToBusinessCenter(List<Long> applyIds) {
        try {
            // 查询申请单
            Map<Long, ApplyDto> applyDtoMapById = selectByApplyIdsAsMap(applyIds);
            // 查询申请单样本
            Map<Long, List<ApplySampleDto>> applySampleMapByApplyId = applySampleService.selectByApplyIds(applyIds)
                    .stream().collect(Collectors.groupingBy(ApplySampleDto::getApplyId));
            // 查询检验项目
            List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplyIds(applyIds);
            Map<Long, List<ApplySampleItemDto>> applySampleItemMapByApplyId =
                    applySampleItemDtos.stream().collect(Collectors.groupingBy(ApplySampleItemDto::getApplyId));

            // 查询出来项目类型为病理类型的项目
            Set<String> barcodes = new HashSet<>();
            for (Map.Entry<Long, List<ApplySampleItemDto>> entry : applySampleItemMapByApplyId.entrySet()) {
                Long applyId = entry.getKey();
                // 取到病理的检验项目
                List<ApplySampleItemDto> pathologyItems =
                        entry.getValue().stream().filter(item -> ItemTypeEnum.PATHOLOGY.name().equals(item.getItemType()))
                                .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(pathologyItems) && Objects.nonNull(applyDtoMapById.get(applyId))) {
                    barcodes.addAll(applySampleMapByApplyId.get(applyId).stream().map(ApplySampleDto::getBarcode)
                            .collect(Collectors.toList()));
                }
            }

            LimsCancelHandSampleRequest request = new LimsCancelHandSampleRequest();
            request.setOrgCode(LoginUserHandler.get().getOrgCode());
            request.setSignBarcodes(new ArrayList<>(barcodes));

            //调用人 信息
            LoginUserHandler.User user = LoginUserHandler.get();
            request.setOptUserId(String.valueOf(user.getUserId()));
            request.setOptUserName(user.getNickname());

            log.info("取消同步手工单到业务中台【参数：{}】", JSON.toJSONString(request));

            Response<?> response = outApplyInfoService.limsCancelHandSample(request);
            if (!response.isSuccess()) {
                log.error("取消同步手工单到业务中台失败【{}】", response.getMsg());
            }
        } catch (Exception e) {
            log.error("取消同步手工单到业务中台失败【参数：{}】", JSON.toJSONString(applyIds));
        }
    }


    @CacheEvict(allEntries = true)
    public void clearCache() {
        // 这方法只用来清除缓存
    }

    /**
     * TbApply 转换为 ApplyDto
     */
    private ApplyDto convert(TbApply tbApply) {
        return JSON.parseObject(JSON.toJSONString(tbApply), ApplyDto.class);
    }

    /**
     * ApplyDto 转换为 TbApply
     */
    private TbApply convert(ApplyDto applyDto) {
        return JSON.parseObject(JSON.toJSONString(applyDto), TbApply.class);
    }

    private LimsPushHandFormRequest.LimsPushHandFormSampleRequest buildSampleRequest(ApplyDto applyDto,
                                                                                     ApplySampleDto applySampleDto, List<ApplySampleItemDto> itemDtos) {
        LimsPushHandFormRequest.LimsPushHandFormSampleRequest sampleRequest =
                new LimsPushHandFormRequest.LimsPushHandFormSampleRequest();

        // 外送到中台的条码号
        sampleRequest.setBarcode(applySampleDto.getBarcode());
        // 申请单类型 门诊/住院
        sampleRequest.setApplyType(applyDto.getApplyTypeName());
        // 门诊/住院 号
        sampleRequest.setPatientVisitCard(applyDto.getPatientVisitCard());
        // 是否加急 0否1是
        sampleRequest.setUrgent(applyDto.getUrgent());
        // 样本类型
        sampleRequest.setSampleType(applySampleDto.getSampleTypeName());
        // 样本形状
        sampleRequest.setSampleProperty(applyDto.getSampleProperty());
        // 申请科室
        sampleRequest.setDept(applyDto.getDept());
        // 病区
        sampleRequest.setInpatientArea(StringPool.EMPTY);
        // 患者名称
        sampleRequest.setPatientName(applyDto.getPatientName());
        // 性别 1男2女
        sampleRequest.setPatientSex(applyDto.getPatientSex());
        // 年龄 xx岁
        sampleRequest.setPatientAge(applyDto.getPatientAge());
        // 子年龄 xxx天|xxx周|xxx月|
        sampleRequest.setPatientSubage(applyDto.getPatientSubage());
        // 子年龄单位
        sampleRequest.setPatientSubageUnit(applyDto.getPatientSubageUnit());
        // 生日
        sampleRequest.setPatientBirthday(applyDto.getPatientBirthday());
        // 床号
        sampleRequest.setPatientBed(applyDto.getPatientBed());
        // 临床诊断
        sampleRequest.setClinicalDiagnosis(applyDto.getDiagnosis());
        // 身份证
        sampleRequest.setPatientCard(applyDto.getPatientCard());
        // 证件类型
        sampleRequest.setPatientCardType(applyDto.getPatientCardType());
        // 住址
        sampleRequest.setPatientAsddress(applyDto.getPatientAddress());
        // 手机号
        sampleRequest.setPatientMobile(applyDto.getPatientMobile());
        // 送检医生
        sampleRequest.setSendDoctor(applyDto.getSendDoctorName());
        // 申请时间（送检时间）
        sampleRequest.setApplyDate(applyDto.getApplyDate());
        // 采样时间
        sampleRequest.setSamplingDate(applyDto.getSamplingDate());
        // 备注
        sampleRequest.setRemark(applyDto.getRemark());

        // 签收人编码
        sampleRequest.setReceiveUserCode(LoginUserHandler.get().getUsername());
        // 签收人名称
        sampleRequest.setReceiveUserName(LoginUserHandler.get().getNickname());
        // 签收时间
        sampleRequest.setReceiveTime(new Date());
        // 签收机构编码
        sampleRequest.setSignOrgCode(LoginUserHandler.get().getOrgCode());
        // 签收机构名称
        sampleRequest.setSignOrgName(LoginUserHandler.get().getOrgName());
        // 签收机构条码
        sampleRequest.setSignBarcode(applySampleDto.getBarcode());

        // 医保卡号
        sampleRequest.setVisitCardNo(StringPool.EMPTY);
        // 管型
        sampleRequest.setTubeType(applySampleDto.getTubeName());

        // 样本数量
        sampleRequest.setSampleNum(applyDto.getSampleCount());
        // 样本包含的检验项目
        sampleRequest.setSampleItemRequests(itemDtos.stream().map(item -> {
            LimsPushHandFormRequest.LimsPushHandFormSampleItemRequest sampleItemRequest =
                    new LimsPushHandFormRequest.LimsPushHandFormSampleItemRequest();
            sampleItemRequest.setTestItemCode(item.getTestItemCode());
            sampleItemRequest.setTestItemName(item.getTestItemName());
            return sampleItemRequest;
        }).collect(Collectors.toList()));

        return sampleRequest;
    }

}
