package com.labway.lims.apply.api.service;

import com.labway.lims.apply.api.dto.*;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.*;

/**
 * 申请单
 */
public interface ApplyService {
    /**
     * 根据id查询
     */
    @Nullable
    ApplyDto selectByApplyId(long applyId);

    /**
     * 根据id查询
     */
    List<ApplyDto> selectByApplyIds(Collection<Long> applyIds);

    /**
     * 查询未复核的申请单 status != 3 并且 根据 ApplySourceEnum.name() 来区分
     *
     * @see com.labway.lims.api.enums.apply.ApplySourceEnum
     */
    @Nonnull
    List<ApplyDto> selectUnreviewedApplySampleByQuery(String applySource, Long hspOrgId);

    /**
     * 添加申请单 & 申请单样本
     *
     * @return 返回主条码
     */
    ApplyInfo addApply(TestApplyDto addApply);

    /**
     * 保存到数据库
     */
    void add(ApplyDto applyDto);

    /**
     * 修改申请单信息
     */
    boolean updateByApplyId(ApplyDto apply);

    /**
     * 删除申请单
     */
    boolean deleteByApplyId(long applyId);

    /**
     * 查询申请单录入双输复核
     */
    List<ApplyDto> selectApplyDoubleCheckList(Long hspOrgId, Date startDate, Date endDate);

    /**
     * 历史结果对比根据送检机构编码(hspOrgCode)、患者姓名(patientName)、性别(patientSex) 三个字段确认为当前患者 如果有身份证优先用身份证查询
     *
     * @param dto ApplyRecordDto
     * @return List<Long>
     */
    Collection<Long> selectByHspOrgIdAndPatientNameAndSex(ApplyRecordDto dto);

    /**
     * 修改申请单
     */
    ApplyInfo update(TestApplyDto update);

    /**
     * 修改申请单和样本信息
     */
    ApplyInfo updateApplySample(TestApplyDto update);

    /**
     * 批量修改申请单信息
     */
    void batchUpdateApply(BatchUpdateApplyDto dto);

    /**
     * 查询已签收列表
     */
    List<ApplyDto> selectHspOrgSignList(HspOrgDateQueryDto queryDto);

    /**
     * 根据签收时间查询已签收列表
     */
    List<ApplyDto> selectSignListBySignDate(Date start, Date end);

    /**
     * 根据 ids 批量修改
     */
    void updateByApplyIds(ApplyDto apply, List<Long> applyIds);

    /**
     * 更新申请单状态
     *
     * @deprecated 调用 {@link #updateByApplyIds(ApplyDto, List)}
     */
    @Deprecated
    void updateCancelCheckStatus(List<Long> applyIds);

    /**
     * 根据各种组合条件查询，但是查询的都是为复核之前的数据
     */
    List<SampleApplyDto> selectUnreviewedApplySampleByQuery(UnreviewedApplyQueryDto query);

    /**
     * 根据申请单id查询申请单样本 & 转成map
     */
    Map<Long, ApplyDto> selectByApplyIdsAsMap(Collection<Long> applyIds);

    /**
     * 根据申请单id查询申请单样本 还有 已删除的但是合并了 & 转成map
     */
    Map<Long, ApplyDto> selectAllByApplyIdsAndMergeAsMap(Collection<Long> applyIds);

    /**
     * 查询 分页 数量
     */
    long selectPageCount(QueryApplyPageDto dto);
    /**
     * 查询 分页
     */
    ApplyPageDto selectPageData(QueryApplyPageDto dto);

    /**
     * 删除申请单 所有信息
     */
    void deleteApply(Collection<Long> applyIds);

    boolean deleteByApplyIds(Collection<Long> applyIds);

    /**
     * 修改基本信息 会记录条码环节
     */
    ApplyDto updateBasicApplyInfo(SimpleTestApplyDto updateTestApply);

    /**
     * 根据主条码查询
     */
    List<ApplyDto> selectByMasterBarcodes(Set<String> masterBarcodes);

    /**
     * 推送病理到业务中台
     */
    void sendPathologyToBusinessCenter(List<Long> applyIds, List<Long> applySampleIds);

    /**
     * 取消推送病理到业务中台
     */
    void cancelSendPathologyToBusinessCenter(List<Long> applyIds);

    /**
     *  项目加减的时候新增条码确认信息
     * @param update
     * @return
     */
    AddSamplesInfoDto updateConfirm(TestApplyDto update);

    /**
     * 根据签收时间查询已签收列表
     */
    List<ApplyDto> selectByDate(Date start, Date end);

}
