package com.labway.lims.routine.service.chain.result;

import com.labway.lims.base.api.dto.InstrumentReportItemRemarkDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <pre>
 * ResultRemarkCommand
 * 结果备注
 * </pre>
 *
 * <AUTHOR>
 * @since 2024/11/27 9:56
 */
@Slf4j
@Component
public class ResultRemarkCommand implements Command {
    private final ExpressionParser expressionParser = new SpelExpressionParser();

    @Override
    public boolean execute(Context c) throws Exception {
        final SaveResultContext context = SaveResultContext.from(c);

        BigDecimal resultForCompare = context.getResultForCompare();
        InstrumentReportItemRemarkDto reportItemRemark = context.getInstrumentReportItemRemark();

        try {
            if (Objects.isNull(reportItemRemark)) {
                return CONTINUE_PROCESSING;
            }

            // 如果结果为空，那么直接加上结果备注
            if (Objects.isNull(resultForCompare)) {
                context.put(SaveResultContext.RESULT_REMARK, reportItemRemark);
            }

            boolean match = false;

            String resultMax = reportItemRemark.getResultMax();
            String resultMaxFormula = reportItemRemark.getResultMaxFormula();
            String resultMin = reportItemRemark.getResultMin();
            String resultMinFormula = reportItemRemark.getResultMinFormula();
            if (StringUtils.isNoneBlank(resultMax, resultMaxFormula) && StringUtils.isNoneBlank(resultMin, resultMinFormula)) {
                // 判断结果上下限
                match = BooleanUtils.isTrue(expressionParser.parseExpression(String.format("%s %s %s && %s %s %s",
                        resultForCompare, resultMinFormula, resultMin,
                        resultForCompare, resultMaxFormula, resultMax)).getValue(Boolean.class));
            } else if (StringUtils.isNoneBlank(resultMax, resultMaxFormula)) {
                // 判断结果上限
                match = BooleanUtils.isTrue(expressionParser.parseExpression(String.format("%s %s %s",
                        resultForCompare, resultMaxFormula, resultMax)).getValue(Boolean.class));
            } else if (StringUtils.isNoneBlank(resultMin, resultMinFormula)) {
                // 判断结果下限
                match = BooleanUtils.isTrue(expressionParser.parseExpression(String.format("%s %s %s",
                        resultForCompare, resultMinFormula, resultMin)).getValue(Boolean.class));
            } else {
                // 如果没有配置结果上下限，直接匹配
                match = true;
            }

            if (match) {
                context.put(SaveResultContext.RESULT_REMARK, reportItemRemark);
            }
        } catch (Exception e) {
            log.info("匹配结果备注异常", e);
        }

        return CONTINUE_PROCESSING;
    }
}
