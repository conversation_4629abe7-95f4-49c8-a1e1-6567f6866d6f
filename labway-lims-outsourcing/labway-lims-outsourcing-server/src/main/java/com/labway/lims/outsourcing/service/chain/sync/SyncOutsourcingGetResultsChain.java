package com.labway.lims.outsourcing.service.chain.sync;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.google.common.collect.ImmutableList;
import com.labway.business.center.compare.dto.OrgApplyResultSamplesDTO;
import com.labway.business.center.compare.dto.TbOrgApplySampleMainIteResultDTO;
import com.labway.business.center.compare.request.QueryLimsOutSourceSampleInfoRequest;
import com.labway.business.center.compare.service.ILimsOutSourceSampleService;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.api.enums.apply.ApplySampleItemSourceEnum;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.enums.base.SystemParamNameEnum;
import com.labway.lims.api.enums.routine.ResultStatusEnum;
import com.labway.lims.api.service.SnowflakeService;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.apply.api.dto.SampleReportDto;
import com.labway.lims.apply.api.dto.UploadPdfDto;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.SampleReportService;
import com.labway.lims.base.api.dto.*;
import com.labway.lims.base.api.service.HspOrganizationService;
import com.labway.lims.base.api.service.SystemParamService;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.base.api.service.UserService;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.pdfreport.api.service.PdfReportService;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import com.labway.lims.routine.api.service.SampleReportItemService;
import com.labway.lims.routine.api.service.SampleResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SyncOutsourcingGetResultsChain implements Command, Filter, InitializingBean {

    public static final String OUT_SAMPLE = IdUtil.simpleUUID();

    @Resource
    private ILimsOutSourceSampleService limsOutSourceSampleService;
    @DubboReference
    private HspOrganizationService hspOrganizationService;
    @DubboReference
    private SampleReportItemService sampleReportItemService;
    @DubboReference
    private SampleResultService sampleResultService;
    @DubboReference
    private SnowflakeService snowflakeService;
    @DubboReference
    private ApplySampleItemService applySampleItemService;
    @DubboReference
    private TestItemService testItemService;
    @DubboReference
    private UserService userService;
    @Resource
    private OutsourcingUser outsourcingUser;
    @Value("${business-center.org-code:00010110000000001WND}")
    private String orgCode;
    @DubboReference
    private SampleReportService sampleReportService;
    @DubboReference
    private SystemParamService systemParamService;
    @DubboReference
    private PdfReportService pdfReportService;

    /**
     * 丹阳调用外部结果流程
     *
     * 根据上海回传的报告项目进行转换后保存落库（删除本地的报告项目和结果，以上海回传的为准），对照的项目来源于业务中台的报告项目，不限于样本外送报告项目
     * 系统如果配置了忽略项目（根据实验室外送的报告项目进行匹配），则不进行回传
     *
     * @param c
     * @return
     * @throws Exception
     */
    @Override
    public boolean execute(Context c) throws Exception {
        final SyncOutsourcingContext context = SyncOutsourcingContext.from(c);

        final Map<Long, List<SampleReportItemDto>> sampleReportItems = sampleReportItemService.selectBySampleIds(context.getOutsourcingSampleIds())
                .stream().collect(Collectors.groupingBy(SampleReportItemDto::getSampleId));

        context.setSampleReportItems(sampleReportItems);

        // 需要删除的报告项目
        Map<Long, List<SampleReportItemDto>> deleteSampleReportItems = context.getDeleteSampleReportItems();
        // 需要删除的样本结果
        Map<Long, List<SampleResultDto>> deleteSampleResults = context.getDeleteSampleResults();
        // 需要新增的样本报告项目
       List<SampleReportItemDto> addSampleReportItemList = context.getAddSampleReportItemList();


        final List<SampleResultDto> results = new LinkedList<>();

        List<String> missingItems = new LinkedList<>();

        LinkedList<Long> ids = snowflakeService.genIds(context.getOutsourcingSamples().size());

        // 查询调用外部结果忽略的报告项目
        List<String> ignoreReportItemCodes = new ArrayList<>();
        final SystemParamDto param = systemParamService.selectByParamName(SystemParamNameEnum.IGNORE_OUTSOURCING_REPORT_ITEM.getCode(), context.getOrgId());
        if (param != null && StringUtils.isNotBlank(param.getParamValue())) {
            JSONArray objects = JSONObject.parseArray(param.getParamValue());
            List<String> ignoreItemCode = objects.stream().map(e -> ((JSONObject)e).getString("reportCode")).collect(Collectors.toList());
            ignoreReportItemCodes.addAll(ignoreItemCode);
        }

        Date now = new Date();

        // 循环每个外送样本
        for (OutsourcingSampleDto e : context.getOutsourcingSamples()) {
            // 外送机构ID
            Long exportOrgId = e.getExportOrgId();

            final QueryLimsOutSourceSampleInfoRequest req = new QueryLimsOutSourceSampleInfoRequest();
            final HspOrganizationDto exportHspOrganization = hspOrganizationService.selectByHspOrgId(exportOrgId);
            if (Objects.isNull(exportHspOrganization)) {
                throw new IllegalStateException(String.format("条码 [%s] 外送机构不存在", e.getBarcode()));
            }

            final HspOrganizationDto hspOrganization = hspOrganizationService.selectByHspOrgId(e.getHspOrgId());
            if (Objects.isNull(hspOrganization)) {
                throw new IllegalStateException(String.format("条码 [%s] 送检机构不存在", e.getBarcode()));
            }

            req.setOrgCode(exportHspOrganization.getHspOrgCode());
            req.setHspOrgCode(orgCode);
            req.setBarCodes(ImmutableList.of(e.getBarcode()));

            log.info("开始调用业务中台，查询外送样本结果信息，查询入参：{}", JSONObject.toJSONString(req));

            final List<OrgApplyResultSamplesDTO> data = limsOutSourceSampleService.queryResultSample(req).getData();

            log.info("调用业务中台查询外送样本结果信息结束，返回信息：{}", JSONObject.toJSONString(data));

            if (CollectionUtils.isEmpty(data)) {
                throw new IllegalStateException(String.format("条码 [%s] 从业务中台获取结果信息失败", e.getBarcode()));
            }

            final OrgApplyResultSamplesDTO p = data.iterator().next();

            // 外送回传结果项目
            List<TbOrgApplySampleMainIteResultDTO> outResultDTOS = ObjectUtils.defaultIfNull(p.getTbOrgApplySampleMainIteResultDTOS(),Collections.emptyList());
            // 移除忽略的报告项目
            outResultDTOS.removeIf(r -> ignoreReportItemCodes.contains(r.getItemReportCode()));
            // 移除空值得报告结果
            outResultDTOS.removeIf(r ->StringUtils.isEmpty(r.getTestResult()));


            // 如果缺少对照关系 那么不同步
            List<TbOrgApplySampleMainIteResultDTO> unCompareResults = p.getTbOrgApplySampleMainIteResultDTOS().stream()
                    .filter(r -> StringUtils.isAnyEmpty(r.getCompareItemReportCode(),
                            r.getCompareItemReportName())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(unCompareResults)){
                log.error("条码 [%s] 检验结果存在未对照的报告项目【报告项目信息：{}】，终止同步结果信息！",unCompareResults.stream()
                        .map(result->result.getItemReportCode()+"|"+result.getItemReportName())
                        .collect(Collectors.joining(",")));
                throw new IllegalStateException(String.format("条码 [%s] 检验结果存在未对照的报告项目，终止同步结果信息！", e.getBarcode()));
            }

            // 填充默认值
            outResultDTOS.forEach(r -> {
                r.setTestJudge(StringUtils.defaultString(r.getTestJudge(),StringUtils.EMPTY));
                r.setResultUnit(StringUtils.defaultString(r.getResultUnit(),StringUtils.EMPTY));
                r.setReferenceValue(StringUtils.defaultString(r.getReferenceValue(),StringUtils.EMPTY));
            });


            // 查询现有的报告项目
            final List<SampleReportItemDto> items = ObjectUtils.defaultIfNull(sampleReportItems.get(e.getOutsourcingSampleId()),Collections.emptyList());
            Map<String, SampleReportItemDto> sampleReportItemDtoMap = items.stream().collect(Collectors.toMap(SampleReportItemDto::getReportItemCode, s -> s, (o1, o2) -> o2));
            // 查询现有结果
            Map<String, SampleResultDto> sampleResultDtoMap = sampleResultService.selectBySampleIdAsMap(e.getOutsourcingSampleId());

            // 回传的所有报告项目编码
            List<String> allReportItemCodes = outResultDTOS.stream().map(TbOrgApplySampleMainIteResultDTO::getCompareItemReportCode).collect(Collectors.toList());

            // 过滤出需要删除报告项目
            final List<SampleReportItemDto> deleteItems = items.stream().filter(i -> !allReportItemCodes.contains(i.getReportItemCode())).collect(Collectors.toList());
            deleteSampleReportItems.put(e.getOutsourcingSampleId(), deleteItems);

            // 过滤出需要删除的报告结果
            final List<SampleResultDto> deleteResults = sampleResultDtoMap.values().stream().filter(i -> !allReportItemCodes.contains(i.getReportItemCode())).collect(Collectors.toList());
            deleteSampleResults.put(e.getOutsourcingSampleId(), deleteResults);


            // 根据回传对照的报告项目进行分组
            final Map<String, TbOrgApplySampleMainIteResultDTO> outResults = outResultDTOS.stream().collect(Collectors.toMap(TbOrgApplySampleMainIteResultDTO::getCompareItemReportCode, v -> v, (a, b) -> a));

            List<SampleReportItemDto> newSampleReportItems = new ArrayList<>();

            // 遍历外送样本的回传结果，循环处理，
            // 如果是外送多出来的，则进行添加（报告项目和报告结果），
            // 如果实验室缺失的则进行删除（报告项目和报告结果）
            outResultDTOS.forEach(o-> {

                // 获取结果数据
                final SampleResultDto result = sampleResultDtoMap.getOrDefault(o.getCompareItemReportCode(), new SampleResultDto());

                // 获取报告项目数据
                SampleReportItemDto reportItem = sampleReportItemDtoMap.get(o.getCompareItemReportCode());
                if (reportItem == null) {
                    reportItem = JSONObject.parseObject(JSONObject.toJSONString(items.get(0)), SampleReportItemDto.class);
                    reportItem.setSampleReportItemId(snowflakeService.genId());
                    reportItem.setReportItemId(-1L);
                    reportItem.setReportItemCode(o.getCompareItemReportCode());
                    reportItem.setReportItemName(o.getCompareItemReportName());
                    reportItem.setTestItemId(-1L);
                    reportItem.setTestItemCode(StringUtils.defaultString(o.getCompareTestItem().get(0).getItemTestCode(),reportItem.getTestItemCode()));
                    reportItem.setTestItemName(StringUtils.defaultString(o.getCompareTestItem().get(0).getItemTestName(),reportItem.getTestItemName()));
                    reportItem.setCreateDate(now);
                    reportItem.setUpdateDate(now);
                    addSampleReportItemList.add(reportItem);
                }


                result.setSampleId(e.getOutsourcingSampleId());
                result.setApplySampleId(e.getApplySampleId());
                result.setApplyId(e.getApplyId());
                result.setTestItemId(reportItem.getTestItemId());
                result.setTestItemCode(reportItem.getTestItemCode());
                result.setTestItemName(reportItem.getTestItemName());
                result.setReportItemId(reportItem.getReportItemId());
                result.setReportItemCode(reportItem.getReportItemCode());
                result.setReportItemName(reportItem.getReportItemName());
                result.setType(StringUtils.EMPTY);
                result.setResult(o.getTestResult());
                result.setUnit(o.getResultUnit());
                result.setRange(o.getReferenceValue());
                if (Objects.equals(YesOrNoEnum.YES.getCode(), o.getIsCritical())) {
                    result.setStatus(ResultStatusEnum.CRISIS.getCode());
                } else if (Objects.equals(YesOrNoEnum.YES.getCode(), o.getIsException())) {
                    result.setStatus(ResultStatusEnum.EXCEPTION.getCode());
                } else {
                    result.setStatus(ResultStatusEnum.NORMAL.getCode());
                }
                result.setInstrumentId(NumberUtils.LONG_ZERO);
                result.setInstrumentName(StringUtils.EMPTY);
                result.setInstrumentResult(StringUtils.EMPTY);
                result.setJudge(o.getTestJudge());
                result.setCreateDate(new Date());
                result.setUpdateDate(new Date());
                result.setCreatorId(LoginUserHandler.get().getUserId());
                result.setCreatorName(LoginUserHandler.get().getNickname());
                result.setUpdaterId(LoginUserHandler.get().getUserId());
                result.setUpdaterName(LoginUserHandler.get().getNickname());
                result.setIsDelete(YesOrNoEnum.NO.getCode());
                result.setInstrumentReportItemReferenceId(NumberUtils.LONG_ZERO);
                results.add(result);
                newSampleReportItems.add(reportItem);
            });

            sampleReportItems.put(e.getOutsourcingSampleId(),newSampleReportItems);

            // 保存达安的PDF报告
            daanReport(e, p);


//            // 添加缺失的样本报告项目
//            String missingReportItem = doSaveMissingReportItem(items, outResults.values(), context.isIgnoreMissingReportItem());
//            if (StringUtils.isNotBlank(missingReportItem)) {
//                missingItems.add(String.format("%s | %s", e.getBarcode(), missingReportItem));
//                continue;
//            }
//            log.info("最终的样本数量:{}", items.size());

            // 添加缺失的样本检验项目
            doSaveMissingTestItem(items);

//            for (SampleReportItemDto t : items) {
//                final TbOrgApplySampleMainIteResultDTO outResult = outResults.get(t.getReportItemCode());
//                if (Objects.isNull(outResult) && !ignoreReportItemCodes.contains(t.getReportItemCode())) {
//                    log.warn("条码 [{}] 存在未对照的预置报告项目 [{}] ，结果值不进行回传填充，请手动填写！！", e.getBarcode(), t.getReportItemName());
//                    throw new IllegalStateException(String.format("条码 [%s] 存在未对照的预置报告项目 [%s]", e.getBarcode(), t.getReportItemName()));
//                    // 南京可以只同步已经对照的项目
//                }else if( Objects.isNull(outResult) && ignoreReportItemCodes.contains(t.getReportItemCode())){
//                    // 没有结果 并且是忽略的报告项目
//                    deleteSampleReportItems.add(t);
//                    log.warn("条码 [{}] 忽略的预置报告项目 [{}] ，结果值不进行回传同步！！", e.getBarcode(), t.getReportItemName());
//                    continue;
//                }
//
//                if (CollectionUtils.isEmpty(outResult.getCompareTestItem())) {
//                    log.error("条码 [{}] 报告项目 [{}] 存在未对照检验项目，结果值不进行回传填充！！！", e.getBarcode(), outResult.getCompareItemReportCode());
//                    throw new IllegalStateException(String.format("条码 [%s] 报告项目 [%s] 存在未对照检验项目", e.getBarcode(), t.getReportItemName()));
//                }
//
//                final SampleResultDto result = sampleResultDtoMap.getOrDefault(t.getReportItemCode(), new SampleResultDto());
//                result.setSampleId(e.getOutsourcingSampleId());
//                result.setApplySampleId(e.getApplySampleId());
//                result.setApplyId(e.getApplyId());
//                result.setTestItemId(t.getTestItemId());
//                result.setTestItemCode(t.getTestItemCode());
//                result.setTestItemName(t.getTestItemName());
//                result.setReportItemId(t.getReportItemId());
//                result.setReportItemCode(t.getReportItemCode());
//                result.setReportItemName(t.getReportItemName());
//                result.setType(StringUtils.EMPTY);
//                result.setResult(outResult.getTestResult());
//                result.setUnit(outResult.getResultUnit());
//                result.setRange(outResult.getReferenceValue());
//                if (Objects.equals(YesOrNoEnum.YES.getCode(), outResult.getIsCritical())) {
//                    result.setStatus(ResultStatusEnum.CRISIS.getCode());
//                } else if (Objects.equals(YesOrNoEnum.YES.getCode(), outResult.getIsException())) {
//                    result.setStatus(ResultStatusEnum.EXCEPTION.getCode());
//                } else {
//                    result.setStatus(ResultStatusEnum.NORMAL.getCode());
//                }
//                result.setInstrumentId(NumberUtils.LONG_ZERO);
//                result.setInstrumentName(StringUtils.EMPTY);
//                result.setInstrumentResult(StringUtils.EMPTY);
//                result.setJudge(outResult.getTestJudge());
//                result.setCreateDate(new Date());
//                result.setUpdateDate(new Date());
//                result.setCreatorId(LoginUserHandler.get().getUserId());
//                result.setCreatorName(LoginUserHandler.get().getNickname());
//                result.setUpdaterId(LoginUserHandler.get().getUserId());
//                result.setUpdaterName(LoginUserHandler.get().getNickname());
//                result.setIsDelete(YesOrNoEnum.NO.getCode());
//                result.setInstrumentReportItemReferenceId(NumberUtils.LONG_ZERO);
//                results.add(result);
//                newSampleReportItems.add(t);
//            }


            context.put(OUT_SAMPLE + e.getOutsourcingSampleId(), p);

            // 需要删除图片的样本ID
            context.getDeleteSampleImageSampleIds().add(e.getOutsourcingSampleId());

            // 添加图片
            if (StringUtils.isNotBlank(p.getImgUrls())) {
                try {
                    for (String imageUrl : JSON.parseArray(p.getImgUrls(), String.class)) {
                        final SampleImageDto k = new SampleImageDto();
                        k.setSampleImageId(ids.pop());
                        k.setApplyId(e.getApplyId());
                        k.setSampleId(e.getOutsourcingSampleId());
                        k.setApplySampleId(e.getApplySampleId());
                        k.setItemType(ItemTypeEnum.OUTSOURCING.name());
                        // 图片名称
                        k.setImageName("达安图片");
                        // 图片地址
                        k.setImageUrl(imageUrl);
                        k.setIsDelete(YesOrNoEnum.NO.getCode());
                        k.setCreateDate(new Date());
                        k.setUpdateDate(new Date());
                        k.setCreatorId(LoginUserHandler.get().getUserId());
                        k.setCreatorName(LoginUserHandler.get().getNickname());
                        k.setUpdaterId(LoginUserHandler.get().getUserId());
                        k.setUpdaterName(LoginUserHandler.get().getNickname());
                        context.getAddSampleImages().add(k);
                    }
                } catch (Exception ex) {
                    log.error("解析达安图片结果异常 {}", p.getImgUrls());
                }
            }

            if (StringUtils.isNotBlank(p.getResultRemark())) {
                // 需要更新的申请单样本
                ApplySampleDto applySampleDto = new ApplySampleDto();
                applySampleDto.setApplySampleId(e.getApplySampleId());
                // 结果备注（建议与解释）
                applySampleDto.setResultRemark(p.getResultRemark());
                context.getUpdateApplySampleDtos().add(applySampleDto);
            }

            // generateTestUser(context, e, p);

        }

        // 更新结果
        List<SampleResultDto> updateSampleResults = results.stream().filter(e -> Objects.nonNull(e.getSampleResultId())).collect(Collectors.toList());
        log.info("更新结果数量 {}", updateSampleResults.size());
        sampleResultService.updateBySampleResultIds(updateSampleResults);
        // 添加结果
        List<SampleResultDto> addSampleResults = results.stream().filter(e -> Objects.isNull(e.getSampleResultId())).collect(Collectors.toList());
        log.info("新增结果数量 {}", addSampleResults.size());
        sampleResultService.addSampleResults(addSampleResults);

        context.setSampleResults(results.stream()
                .collect(Collectors.groupingBy(SampleResultDto::getSampleId)));

        return CONTINUE_PROCESSING;
    }

    private void daanReport(OutsourcingSampleDto e, OrgApplyResultSamplesDTO p) {
        Long exportOrgId = e.getExportOrgId();
        // TODO: 2024/4/2 判断是否是达安
        String reportUrls = p.getReportUrls();
        if (StringUtils.isNotBlank(reportUrls) && CollectionUtils.isNotEmpty(JSON.parseArray(reportUrls, String.class))) {

            UploadPdfDto uploadPdfDto = new UploadPdfDto();
            uploadPdfDto.setApplySampleId(e.getApplySampleId());
            List<String> urls = JSON.parseArray(reportUrls, String.class);
            if (urls.size() > 1) {
                String url = pdfReportService.mergePdfByUrls2Url(urls);
                uploadPdfDto.setUrl(url);
            } else {
                uploadPdfDto.setUrl(urls.get(0));
            }

            SampleReportDto sampleReportDto = sampleReportService.uploadReport(uploadPdfDto);

            log.info("保存达安结果报告：{}", JSON.toJSONString(sampleReportDto));
        }
    }

    private boolean generateTestUser(SyncOutsourcingContext context, OutsourcingSampleDto e, OrgApplyResultSamplesDTO p) {
        try {
            OutsourcingUser syncTestUser = new OutsourcingUser();
            // 填充检验人
            Long testerId = getOutSideUserId(outsourcingUser.getTesterSign(), outsourcingUser.getTesterName(), p, e);
            Long checkerId = getOutSideUserId(outsourcingUser.getCheckerSign(), outsourcingUser.getCheckerName(), p, e);

            syncTestUser.setTesterId(testerId);
            syncTestUser.setTesterName(outsourcingUser.getTesterName());
            syncTestUser.setCheckerId(checkerId);
            syncTestUser.setCheckerName(outsourcingUser.getCheckerName());

            context.getTesterAuditorMap().put(p.getBarcode(), syncTestUser);
            return true;
        } catch (Exception ex) {
            log.error("生成审核人信息异常 条码号 [{}] \n ", e.getBarcode(), ex);
            return false;
        }
    }

    // 查询审核人信息
    private Long getOutSideUserId(String cnSign, String userName, OrgApplyResultSamplesDTO dto, OutsourcingSampleDto e) {
        UserDto userDto = userService.selectByUsernameAndOrgId(userName, e.getExportOrgId());
        if (userDto != null) {
            return userDto.getUserId();
        }

        log.info("新增外部用户...");
        AddUserDto addUserDto = new AddUserDto();
        addUserDto.setUsername(userName);
        addUserDto.setNickname(userName);
        addUserDto.setStatus(1);
        addUserDto.setEnSign("");
        addUserDto.setCnSign(cnSign);
        addUserDto.setRoleIds(Collections.emptySet());
        addUserDto.setRoleId(0L);
        addUserDto.setGroupIds(Collections.emptySet());
        addUserDto.setGroupId(0L);
        addUserDto.setSex(0);
        addUserDto.setOrgId(e.getExportOrgId());
        addUserDto.setOrgCode(dto.getSignOrgCode());
        addUserDto.setOrgName(e.getExportOrgName());

        return userService.addOutUser(addUserDto);
    }

    // 添加缺失的样本检验项目
    private void doSaveMissingTestItem(List<SampleReportItemDto> items) {
        if (CollectionUtils.isEmpty(items)) {
            return;
        }

        SampleReportItemDto sampleReportItemDto = items.get(0);
        log.info("样本第一个检验项目信息:{}", JSONObject.toJSONString(sampleReportItemDto));

        List<String> testItemCodes = items.stream().map(SampleReportItemDto::getTestItemCode).collect(Collectors.toList());
        Map<String, TestItemDto> testItemCodeMap = testItemService.selectByTestItemCodes(testItemCodes,
                LoginUserHandler.get().getOrgId()).stream().collect(Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (a, b) -> a));

        // 查询外送的检验项目
        List<ApplySampleItemDto> applySampleItemDtos = applySampleItemService.selectByApplySampleId(sampleReportItemDto.getApplySampleId());
        List<String> existTestItemCodes = applySampleItemDtos.stream().map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toList());

        // 缺少的外送检验项目
        List<ApplySampleItemDto> addSampleItems = new ArrayList<>();
        Date now = new Date();
        for (SampleReportItemDto item : items) {
            String tempCode = item.getTestItemCode();
            if (StringUtils.isBlank(tempCode) || existTestItemCodes.contains(tempCode)) {
                continue;
            }
            if (addSampleItems.stream().map(ApplySampleItemDto::getTestItemCode).collect(Collectors.toList()).contains(tempCode)) {
                continue;
            }

            ApplySampleItemDto tempDto = new ApplySampleItemDto();
            BeanUtils.copyProperties(sampleReportItemDto, tempDto);
            tempDto.setApplySampleItemId(snowflakeService.genId());
            tempDto.setTestItemId(item.getTestItemId());
            tempDto.setTestItemCode(item.getTestItemCode());
            tempDto.setTestItemName(item.getTestItemName());

            tempDto.setCreateDate(now);
            tempDto.setCreatorId(LoginUserHandler.get().getUserId());
            tempDto.setCreatorName(LoginUserHandler.get().getNickname());
            tempDto.setUpdateDate(now);
            tempDto.setUpdaterId(LoginUserHandler.get().getUserId());
            tempDto.setUpdaterName(LoginUserHandler.get().getNickname());
            tempDto.setGroupId(LoginUserHandler.get().getGroupId());
            tempDto.setGroupName(LoginUserHandler.get().getGroupName());

            tempDto.setRemark(StringPool.EMPTY);
            tempDto.setIsDelete(YesOrNoEnum.NO.getCode());
            tempDto.setItemType(ItemTypeEnum.OUTSOURCING.name());
            tempDto.setOutTestItemId(NumberUtils.LONG_ZERO);
            tempDto.setOutTestItemCode(StringUtils.EMPTY);
            tempDto.setOutTestItemName(StringUtils.EMPTY);
            tempDto.setUrgent(YesOrNoEnum.NO.getCode());
            tempDto.setCount(NumberUtils.INTEGER_ONE);
            tempDto.setSplitCode(StringPool.EMPTY);

            if (testItemCodeMap.containsKey(item.getTestItemCode())) {
                TestItemDto testItemDto = testItemCodeMap.get(item.getTestItemCode());

                tempDto.setSampleTypeCode(testItemDto.getSampleTypeCode());
                tempDto.setSampleTypeName(testItemDto.getSampleTypeName());
                tempDto.setTubeCode(testItemDto.getTubeCode());
                tempDto.setTubeName(testItemDto.getTubeName());

                tempDto.setExportOrgId(testItemDto.getExportOrgId());
                tempDto.setExportOrgName(testItemDto.getExportOrgName());
                tempDto.setIsOutsourcing(testItemDto.getEnableExport());
                tempDto.setIsFree(testItemDto.getEnableFee() ^ 1);
                tempDto.setFeePrice(testItemDto.getFeePrice());
                tempDto.setActualFeePrice(BigDecimal.ZERO);
                tempDto.setItemSource(ApplySampleItemSourceEnum.DEFAULT_SOURCE.getCode());
            }

            addSampleItems.add(tempDto);
        }
        // 保存样本的检验项目
        // applySampleItemService.addApplySampleItems(addSampleItems);
        log.info("新增样本检验项目信息:{}", JSONObject.toJSONString(addSampleItems));
    }

    // 添加缺失的报告项目
    private String doSaveMissingReportItem(
            List<SampleReportItemDto> items,
            Collection<TbOrgApplySampleMainIteResultDTO> outResults,
            boolean ignoreMissingReportItem) {
        if (CollectionUtils.isEmpty(outResults) || CollectionUtils.isEmpty(items)) {
            log.error("样本项目为空，或者外部回传结果为空！！！");
            throw new IllegalStateException("样本项目为空，或者外部回传结果为空！！！");
        }

        // 实验室已经存在的报告项目
        List<String> existCodes = items.stream().map(SampleReportItemDto::getReportItemCode).collect(Collectors.toList());
        // 回传结果的报告项目编码
        List<String> outReportCodes = outResults.stream().map(TbOrgApplySampleMainIteResultDTO::getCompareItemReportCode).collect(Collectors.toList());

        // 缺失的报告项目
        List<TbOrgApplySampleMainIteResultDTO> missingReportItems = outResults.stream().filter(e -> !existCodes.contains(e.getCompareItemReportCode())).collect(Collectors.toList());
        // List<Long> deleteIds = items.stream().filter(e -> !outReportCodes.contains(e.getReportItemCode())).map(e -> e.getSampleReportItemId()).collect(Collectors.toList());

        // 填充转换缺失的报告项目
        Date now = new Date();
        SampleReportItemDto sampleReportItemDto = items.get(0);
        List<SampleReportItemDto> addSampleReportItems = new ArrayList<>();
        for (TbOrgApplySampleMainIteResultDTO missingReportItem : missingReportItems) {
            if (CollectionUtils.isEmpty(missingReportItem.getCompareTestItem())) {
                log.error("外部回传结果的报告项目编码 [{}] 未对照检验项目！！！", missingReportItem.getCompareItemReportCode());
                continue;
            }

            SampleReportItemDto tempDto = new SampleReportItemDto();
            BeanUtils.copyProperties(sampleReportItemDto, tempDto);
            tempDto.setSampleReportItemId(snowflakeService.genId());
            tempDto.setReportItemId(-1L);
            tempDto.setReportItemCode(missingReportItem.getCompareItemReportCode());
            tempDto.setReportItemName(missingReportItem.getCompareItemReportName());
            tempDto.setTestItemId(-1L);
            tempDto.setTestItemCode(missingReportItem.getCompareTestItem().get(0).getItemTestCode());
            tempDto.setTestItemName(missingReportItem.getCompareTestItem().get(0).getItemTestName());
            tempDto.setCreateDate(now);
            tempDto.setUpdateDate(now);
            addSampleReportItems.add(tempDto);
        }

        String missingItems = "";
        log.info("添加缺失的报告项目【{}】", JSON.toJSONString(addSampleReportItems));

        if (!ignoreMissingReportItem && CollectionUtils.isNotEmpty(addSampleReportItems)) {
            missingItems = addSampleReportItems.stream().map(SampleReportItemDto::getReportItemName).collect(Collectors.joining(","));
        }

        return missingItems;
    }

    OrgApplyResultSamplesDTO getOutSample(Context c, Long outsourcingSampleId) {
        return (OrgApplyResultSamplesDTO) c.get(OUT_SAMPLE + outsourcingSampleId);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
    }

    @Override
    public boolean postprocess(Context c, Exception exception) {
        if (Objects.isNull(exception)) {
            return CONTINUE_PROCESSING;
        }

        try {
            if(Boolean.TRUE){
                return CONTINUE_PROCESSING;
            }
            // 遇到错误时，删除录入的结果
            sampleReportItemService.deleteBySampleIds(SyncOutsourcingContext.from(c).getOutsourcingSampleIds());
        } catch (Exception ignored) {

        }

        return CONTINUE_PROCESSING;
    }
}
