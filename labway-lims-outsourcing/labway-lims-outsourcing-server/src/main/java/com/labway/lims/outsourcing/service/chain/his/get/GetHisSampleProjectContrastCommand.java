package com.labway.lims.outsourcing.service.chain.his.get;

import com.labway.business.center.compare.dto.OutApplyInfoDTO;
import com.labway.lims.api.LoginUserHandler;
import com.labway.lims.api.enums.base.ItemTypeEnum;
import com.labway.lims.api.exception.LimsCodeException;
import com.labway.lims.apply.api.service.ApplySampleItemService;
import com.labway.lims.apply.api.service.ApplyService;
import com.labway.lims.base.api.dto.TestItemDto;
import com.labway.lims.base.api.service.TestItemService;
import com.labway.lims.outsourcing.api.dto.his.HisGetParam;
import com.labway.lims.outsourcing.api.dto.his.HisSampleItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class GetHisSampleProjectContrastCommand implements Command {

    @DubboReference
    private ApplyService applyService;

    @DubboReference
    private TestItemService testItemService;

    @DubboReference
    private ApplySampleItemService applySampleItemService;

    @Override
    public boolean execute(Context context) throws Exception {
        final GetHisSampleContext from = GetHisSampleContext.from(context);

        final HisGetParam hisGetParam = from.getHisGetParam();

        final String outBarcode = hisGetParam.getOutBarcode();

        final OutApplyInfoDTO outApplyInfo = from.getOutApplyInfo();
        final List<OutApplyInfoDTO.OutApplyItem> outApplyItems =
                outApplyInfo.getItems();

        final Set<String> testItemCodes = outApplyItems.stream()
                .filter(f -> CollectionUtils.isNotEmpty(f.getItems()))
                .flatMap(m -> m.getItems().stream().map(OutApplyInfoDTO.TestItem::getTestItemCode))
                .collect(Collectors.toSet());

        final List<TestItemDto> testItems = testItemService
                .selectByTestItemCodes(testItemCodes, LoginUserHandler.get().getOrgId());
        final Map<String, TestItemDto> testItemMap = testItems.stream()
                .collect(Collectors.toMap(TestItemDto::getTestItemCode, Function.identity(), (a, b) -> a));

        // 检验项目信息
        final List<HisSampleItem> hisSampleItems = new LinkedList<>();

        // 没有对照检验项目的外部项目编码
        Set<String> noMatchTestItemCodes = new HashSet<>();

        // 校验外部项目是否对照 LIMS 检验项目
        for (final OutApplyInfoDTO.OutApplyItem outApplyItem : outApplyItems) {
            final String outTestItemName = outApplyItem.getOutTestItemName();
            final String outTestItemCode = outApplyItem.getOutTestItemCode();
            if (StringUtils.isBlank(outTestItemName) || StringUtils.isBlank(outTestItemCode)) {
                throw new IllegalStateException("条码项目信息不完整");
            }

            // 对照信息
            final List<OutApplyInfoDTO.TestItem> items = outApplyItem.getItems();
            if (CollectionUtils.isEmpty(items)) {
                // 如果没对照先跳过
                log.warn(String.format("外部条码 [%s] HIS项目名称 [%s] 编码 [%s] ，未对照LIMS检验项目", outBarcode, outTestItemName, outTestItemCode));
                noMatchTestItemCodes.add(outTestItemCode);
                continue;
            }

            for (final OutApplyInfoDTO.TestItem item : items) {
                final TestItemDto testItem = testItemMap.get(item.getTestItemCode());
                if (Objects.isNull(testItem)) {
                    throw new IllegalStateException(String.format("检验项目名称 [%s] 编码 [%s] 在LIMS中不存在",
                            item.getTestItemName(), item.getTestItemCode()));
                }

                // 构建 HIS检验项目信息
                HisSampleItem hisSampleItem = new HisSampleItem();
                hisSampleItem.setOutTestItemName(StringUtils.defaultString(outApplyItem.getOutTestItemName()));
                hisSampleItem.setOutTestItemCode(StringUtils.defaultString(outApplyItem.getOutTestItemCode()));
                //
                hisSampleItem.setTestItemCode(StringUtils.defaultString(testItem.getTestItemCode()));
                hisSampleItem.setTestItemName(StringUtils.defaultString(testItem.getTestItemName()));
                hisSampleItem.setGroupId(testItem.getGroupId());
                hisSampleItem.setGroupName(StringUtils.defaultString(testItem.getGroupName()));
                hisSampleItem.setItemType(StringUtils.defaultString(testItem.getItemType()));
                hisSampleItem.setTestItemId(testItem.getTestItemId());
                hisSampleItem.setTubeCode(StringUtils.defaultString(testItem.getTubeCode()));
                hisSampleItem.setTubeName(StringUtils.defaultString(testItem.getTubeName()));
                hisSampleItem.setSampleTypeCode(StringUtils.defaultString(testItem.getSampleTypeCode()));
                hisSampleItem.setSampleType(StringUtils.defaultString(testItem.getSampleTypeName()));
                //
                hisSampleItem.setCount(ObjectUtils.defaultIfNull(item.getCount(), NumberUtils.INTEGER_ONE));
                hisSampleItem.setUrgent(ObjectUtils.defaultIfNull(item.getUrgent(), NumberUtils.INTEGER_ZERO));

                // 如果是微生物样本，使用送检机构的样本类型
                // https://www.tapd.cn/59091617/prong/stories/view/1159091617001000875
                if (Objects.equals(ItemTypeEnum.MICROBIOLOGY.name(), testItem.getItemType())) {
                    if (StringUtils.isNotBlank(outApplyInfo.getSampleType())) {
                        hisSampleItem.setSampleTypeCode(outApplyInfo.getSampleType());
                        hisSampleItem.setSampleType(outApplyInfo.getSampleType());
                    }
                }

                hisSampleItem.setHisSampleItemId(hisSampleItem.getOutTestItemCode() + "-" + hisSampleItem.getTestItemCode());
                hisSampleItems.add(hisSampleItem);
            }
        }

        // 如果不为空 那就代表着有未对照的外部项目
        if (CollectionUtils.isNotEmpty(noMatchTestItemCodes)) {
            // 1120 代表着未对照的外部项目 的错误码
            final LimsCodeException limsCodeException = new LimsCodeException(1120, String.format("外部项目 [%s] 未对照检验项目",
                    String.join(",", noMatchTestItemCodes)));
            limsCodeException.setData(noMatchTestItemCodes);
            throw limsCodeException;
        }

        if (CollectionUtils.isEmpty(hisSampleItems)) {
            throw new IllegalStateException("条码项目信息为空");
        }


        from.put(GetHisSampleContext.HIS_SAMPLE_ITEMS, hisSampleItems);

        return CONTINUE_PROCESSING;
    }
}
