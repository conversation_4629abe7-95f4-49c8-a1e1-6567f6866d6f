package com.labway.lims.outsourcing.service.chain.sync;

import cn.hutool.core.util.IdUtil;
import com.labway.lims.apply.api.dto.ApplyDto;
import com.labway.lims.apply.api.dto.ApplySampleDto;
import com.labway.lims.apply.api.dto.SampleImageDto;
import com.labway.lims.outsourcing.api.dto.OutsourcingSampleDto;
import com.labway.lims.outsourcing.service.chain.StopWatchContext;
import com.labway.lims.routine.api.dto.SampleReportItemDto;
import com.labway.lims.routine.api.dto.SampleResultDto;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.chain.Context;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @since 2023/4/3 15:45
 */
@Getter
@Setter
@SuppressWarnings("unchecked")
public class SyncOutsourcingContext extends StopWatchContext {

    /**
     * 要更新的申请单样本
     */
    static final String UPDATE_APPLY_SAMPLE = "UPDATE_APPLY_SAMPLE_" + IdUtil.objectId();
    /**
     * 新增的样本图片
     */
    static final String ADD_SAMPLE_IMAGES = "ADD_SAMPLE_IMAGES_" + IdUtil.objectId();

    /**
     * 要删除图片的样本ID
     */
    static final String DELETE_SAMPLE_IMAGE_SAMPLE_IDS = "DELETE_SAMPLE_IMAGE_SAMPLE_IDS_" + IdUtil.objectId();

    /**
     * 审核 外送样本id
     */
    private Collection<Long> outsourcingSampleIds;

    /**
     * apply_sample
     */
    private Map<Long, ApplySampleDto> applySamples;

    /**
     * OutsourcingSampleDto
     */
    private List<OutsourcingSampleDto> outsourcingSamples;

    /**
     * 样本报告项目 key: 样本ID
     */
    private Map<Long, List<SampleReportItemDto>> sampleReportItems;

    /**
     * 样本结果 key: 样本ID
     */
    private Map<Long, List<SampleResultDto>> sampleResults;

    /**
     * 根据结果删除原有的样本报告项目
     */
    private Map<Long, List<SampleReportItemDto>> deleteSampleReportItems = new HashMap<>();
    /**
     * 根据结果删除原有的样本结果
     */
    private Map<Long, List<SampleResultDto>> deleteSampleResults = new HashMap<>();
    /**
     * 根据结果新增样本报告项目
     */
    private Map<Long, List<SampleReportItemDto>> addSampleReportItems;

    /**
     * 需要新增的样本报告项目
     */
    private List<SampleReportItemDto> addSampleReportItemList = new ArrayList<>();

    /**
     * 申请单
     */
    private Map<Long, ApplyDto> applies = Collections.emptyMap();

    /**
     * 调用外部 操作人
     */
    private String operator;

    /**
     * 机构id
     */
    private Long orgId;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 是否忽略多出的报告项目
     */
    private boolean ignoreMissingReportItem;

    private Map<String, OutsourcingUser> testerAuditorMap = new HashMap<>();
    private OutsourcingUser DEFAULT = new OutsourcingUser();

    public OutsourcingUser getTesterAuditor(String barcode) {
        return testerAuditorMap.get(barcode);
    }

    public static SyncOutsourcingContext from(Context c) {
        return (SyncOutsourcingContext) c;
    }

    public List<ApplySampleDto> getUpdateApplySampleDtos() {
        return (List<ApplySampleDto>) computeIfAbsent(UPDATE_APPLY_SAMPLE, key -> new ArrayList<>());
    }

    public List<SampleImageDto> getAddSampleImages() {
        return (List<SampleImageDto>) computeIfAbsent(ADD_SAMPLE_IMAGES, key -> new ArrayList<>());
    }

    public Set<Long> getDeleteSampleImageSampleIds() {
        return (Set<Long>) computeIfAbsent(DELETE_SAMPLE_IMAGE_SAMPLE_IDS, key -> new HashSet<>());
    }

    @Override
    protected String getWatchName() {
        return "调用外部结果";
    }
}
