package com.labway.lims.outsourcing.service.chain.pick.two;

import com.labway.lims.api.enums.YesOrNoEnum;
import com.labway.lims.apply.api.dto.ApplySampleItemDto;
import com.labway.lims.base.api.dto.InstrumentReportItemDto;
import com.labway.lims.base.api.dto.ReportItemDto;
import com.labway.lims.base.api.service.InstrumentReportItemService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.chain.Command;
import org.apache.commons.chain.Context;
import org.apache.commons.chain.Filter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.SpelNode;
import org.springframework.expression.spel.ast.CompoundExpression;
import org.springframework.expression.spel.ast.VariableReference;
import org.springframework.expression.spel.standard.SpelExpression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;

import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2023/7/20 10:30
 */
@Slf4j
@Component
public class OutSourcingTwoPickRecalculateCommand implements Filter, Command {

    final ExpressionParser expressionParser = new SpelExpressionParser();
    @DubboReference
    private InstrumentReportItemService instrumentReportItemService;

    @Override
    public boolean postprocess(Context context, Exception exception) {
        return CONTINUE_PROCESSING;
    }

    @Override
    public boolean execute(Context c) throws Exception {
        final OutsourcingTwoPickContext context = OutsourcingTwoPickContext.from(c);

        //待分拣的报告项目
        final List<ReportItemDto> reportItems = context.getReportItems();
        // 报告项目对应的检验项目
        Map<String, Long> reportItemCode2TestItemIdMap = context.getReportItemCode2TestItemIdMap();
        List<ApplySampleItemDto> applySampleItems = context.getApplySampleItems();

        //当前仪器下的报告项目
        final LinkedList<InstrumentReportItemDto> insReportItems = context.getInstrumentReportItems().stream()
                .filter(e -> Objects.equals(e.getIsBringOut(), YesOrNoEnum.YES.getCode()))
                .collect(Collectors.toCollection(LinkedList::new));

        if (CollectionUtils.isEmpty(insReportItems)) {
            return CONTINUE_PROCESSING;
        }

        final Set<String> currentReportItemCodes = reportItems.stream().map(ReportItemDto::getReportItemCode)
                .collect(Collectors.toSet());

        // 重试一百次
        for (int i = 0; i < 100 && !insReportItems.isEmpty(); i++) {
            final InstrumentReportItemDto e = insReportItems.removeFirst();
            if (StringUtils.isBlank(e.getCalcFomulation())) {
                continue;
            }

            // 计算公式依赖的检验报告项目
            final Set<String> refs = getAllVariableReference((SpelExpression) expressionParser
                    .parseExpression(StringUtils.replace(e.getCalcFomulation(), "#", "#_")))
                    .stream().map(SpelNode::toStringAST)
                    .map(k -> StringUtils.removeStart(k, "#_"))
                    // 包含 . 的是内置变量，过滤掉
                    .filter(k -> !StringUtils.contains(k, "."))
                    .collect(Collectors.toSet());

            // 如果不包含 继续重试
            if (!CollectionUtils.containsAll(currentReportItemCodes, refs)) {
                insReportItems.addLast(e);
                continue;
            }

            // 包含跳出
            if (currentReportItemCodes.contains(e.getReportItemCode())) {
                continue;
            }

            currentReportItemCodes.add(e.getReportItemCode());

            // 根据报告项目 找到依赖的检验项目
            Set<Long> belongTestItemIds = refs.stream().map(reportItemCode2TestItemIdMap::get).collect(Collectors.toSet());
            ApplySampleItemDto applySampleItem = applySampleItems.stream()
                    .filter(item -> belongTestItemIds.contains(item.getTestItemId()))
                    .findAny().orElse(applySampleItems.iterator().next());

            reportItems.add(convertToReportItem(e, applySampleItem));
        }

        return CONTINUE_PROCESSING;
    }


    public Set<SpelNode> getAllVariableReference(SpelExpression expression) {
        final HashSet<SpelNode> list = new HashSet<>();
        doGetAllVariableReference(expression.getAST(), list);
        return list;
    }

    private void doGetAllVariableReference(SpelNode node, Set<SpelNode> list) {

        if (node instanceof VariableReference) {
            list.add(node);
        } else if (node instanceof CompoundExpression) {
            list.add(node);
            return;
        }

        for (int i = 0; i < node.getChildCount(); i++) {
            final SpelNode child = node.getChild(i);
            doGetAllVariableReference(child, list);
        }
    }

    private ReportItemDto convertToReportItem(InstrumentReportItemDto item, ApplySampleItemDto applySampleItem) {
        final ReportItemDto dto = new ReportItemDto();
        dto.setReportItemId(NumberUtils.LONG_ZERO);
        dto.setReportItemName(item.getReportItemName());
        dto.setReportItemCode(item.getReportItemCode());
        dto.setTestItemId(applySampleItem.getTestItemId());
        dto.setTestItemCode(applySampleItem.getTestItemCode());
        dto.setTestItemName(applySampleItem.getTestItemName());
        return dto;
    }

}
